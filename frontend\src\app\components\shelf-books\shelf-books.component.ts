import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
// import { MatChipsModule } from '@angular/material/chips'; // Not available in Angular 18
import { MatTabsModule } from '@angular/material/tabs';

import { ShelfService } from '../../services/shelf.service';
import { BookService } from '../../services/book.service';
import { Shelf, BookInShelf, AssignBookToShelf, RemoveBookFromShelf, ShelfLocations } from '../../models/shelf.model';
import { Book } from '../../models/book.model';

@Component({
  selector: 'app-shelf-books',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatSnackBarModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    // MatChipsModule, // Not available
    MatTabsModule
  ],
  template: `
    <div class="shelf-books-container">
      <!-- Header -->
      <mat-card class="header-card">
        <mat-card-header>
          <div mat-card-avatar class="shelf-avatar">
            <mat-icon>inventory</mat-icon>
          </div>
          <mat-card-title>{{ shelf?.name }}</mat-card-title>
          <mat-card-subtitle>
            Quản lý sách trong kệ - {{ shelf?.zoneName }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div class="shelf-stats">
            <div class="stat-item">
              <mat-icon>book</mat-icon>
              <span>{{ booksInShelf.length }} sách</span>
            </div>
            <div class="stat-item">
              <mat-icon>inventory_2</mat-icon>
              <span>{{ shelf?.currentCount }}/{{ shelf?.capacity }}</span>
            </div>
            <div class="stat-item">
              <mat-icon>space_bar</mat-icon>
              <span>{{ shelf?.availableSpace }} chỗ trống</span>
            </div>
          </div>
        </mat-card-content>

        <mat-card-actions align="end">
          <button mat-button (click)="goBack()">
            <mat-icon>arrow_back</mat-icon>
            Quay lại
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Tabs -->
      <mat-tab-group>
        <!-- Books in Shelf Tab -->
        <mat-tab label="Sách trong kệ">
          <div class="tab-content">
            <mat-card>
              <mat-card-header>
                <mat-card-title>Danh sách sách trong kệ</mat-card-title>
              </mat-card-header>

              <mat-card-content>
                <div *ngIf="booksInShelf.length === 0" class="empty-state">
                  <mat-icon class="empty-icon">book_online</mat-icon>
                  <h3>Chưa có sách nào</h3>
                  <p>Kệ này chưa có sách nào được xếp vào</p>
                </div>

                <table mat-table [dataSource]="booksInShelf" *ngIf="booksInShelf.length > 0">
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Tên sách</th>
                    <td mat-cell *matCellDef="let book">
                      <div class="book-title">
                        <strong>{{ book.title }}</strong>
                        <small>{{ book.author }}</small>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="category">
                    <th mat-header-cell *matHeaderCellDef>Thể loại</th>
                    <td mat-cell *matCellDef="let book">
                      <span class="category-chip">{{ book.categoryName }}</span>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="location">
                    <th mat-header-cell *matHeaderCellDef>Vị trí</th>
                    <td mat-cell *matCellDef="let book">
                      <div class="location-info">
                        <mat-icon>place</mat-icon>
                        <span>{{ book.locationCode || 'Chưa xác định' }}</span>
                      </div>
                    </td>
                  </ng-container>



                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Thao tác</th>
                    <td mat-cell *matCellDef="let book">
                      <button mat-icon-button color="warn" 
                              (click)="removeBookFromShelf(book)"
                              [disabled]="isLoading">
                        <mat-icon>remove_circle</mat-icon>
                      </button>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Add Books Tab -->
        <mat-tab label="Thêm sách">
          <div class="tab-content">
            <mat-card>
              <mat-card-header>
                <mat-card-title>Thêm sách vào kệ</mat-card-title>
                <mat-card-subtitle>Chọn sách từ kho để thêm vào kệ</mat-card-subtitle>
              </mat-card-header>

              <mat-card-content>
                <div class="add-book-form">
                  <mat-form-field appearance="outline" class="book-select">
                    <mat-label>Chọn sách</mat-label>
                    <mat-select [(value)]="selectedBookId" placeholder="Chọn sách từ kho">
                      <mat-option *ngFor="let book of booksInStorage" [value]="book.id">
                        <div class="book-option">
                          <strong>{{ book.title }}</strong>
                          <small>{{ book.author }} - {{ book.categoryName }}</small>
                          <span class="available-chip">
                            {{ book.stockQuantity }} cuốn có sẵn
                          </span>
                        </div>
                      </mat-option>
                    </mat-select>
                  </mat-form-field>



                  <mat-form-field appearance="outline" class="location-select">
                    <mat-label>Vị trí (tùy chọn)</mat-label>
                    <mat-select [(value)]="selectedLocationCode" placeholder="Chọn vị trí"
                                (selectionChange)="onLocationChange()">
                      <mat-option value="">Không xác định</mat-option>
                      <mat-option *ngFor="let location of availableLocations" [value]="location">
                        {{ location }}
                      </mat-option>
                    </mat-select>

                  </mat-form-field>

                  <button mat-raised-button color="primary" 
                          (click)="assignBookToShelf()"
                          [disabled]="!canAssignBook() || isLoading">
                    <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
                    <mat-icon *ngIf="!isLoading">add</mat-icon>
                    Thêm vào kệ
                  </button>
                </div>


              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Locations Tab -->
        <mat-tab label="Vị trí">
          <div class="tab-content">
            <mat-card>
              <mat-card-header>
                <mat-card-title>Quản lý vị trí trong kệ</mat-card-title>
                <mat-card-subtitle>Xem và quản lý các vị trí trong kệ sách</mat-card-subtitle>
              </mat-card-header>

              <mat-card-content>
                <div class="locations-grid">
                  <div class="location-section">
                    <h4>
                      <mat-icon>check_circle</mat-icon>
                      Vị trí đã sử dụng ({{ shelfLocations?.usedLocations?.length || 0 }})
                    </h4>
                    <div class="location-chips">
                      <span *ngFor="let location of shelfLocations?.usedLocations"
                            class="location-chip used">
                        {{ location }}
                      </span>
                    </div>
                  </div>

                  <div class="location-section">
                    <h4>
                      <mat-icon>radio_button_unchecked</mat-icon>
                      Vị trí còn trống ({{ shelfLocations?.availableLocations?.length || 0 }})
                    </h4>
                    <div class="location-chips">
                      <span *ngFor="let location of shelfLocations?.availableLocations"
                            class="location-chip available">
                        {{ location }}
                      </span>
                    </div>
                  </div>
                </div>

                <div class="capacity-info">
                  <h4>Thông tin sức chứa</h4>
                  <div class="capacity-stats">
                    <div class="capacity-stat">
                      <span class="label">Tổng sức chứa:</span>
                      <span class="value">{{ shelfLocations?.capacity }} cuốn</span>
                    </div>
                    <div class="capacity-stat">
                      <span class="label">Đã sử dụng:</span>
                      <span class="value">{{ shelfLocations?.currentCount }} cuốn</span>
                    </div>
                    <div class="capacity-stat">
                      <span class="label">Còn trống:</span>
                      <span class="value">{{ (shelfLocations?.capacity || 0) - (shelfLocations?.currentCount || 0) }} cuốn</span>
                    </div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>

    <!-- Loading overlay -->
    <div *ngIf="isLoading" class="loading-overlay">
      <mat-spinner diameter="50"></mat-spinner>
    </div>
  `,
  styles: [`
    .shelf-books-container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 0 16px;
    }

    .header-card {
      margin-bottom: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .header-card mat-card-title,
    .header-card mat-card-subtitle {
      color: white;
    }

    .shelf-avatar {
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .shelf-stats {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }

    .tab-content {
      padding: 20px 0;
    }

    .book-title {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .book-title small {
      color: rgba(0, 0, 0, 0.6);
      font-size: 12px;
    }

    .location-info {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .location-info mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .add-book-form {
      display: flex;
      gap: 16px;
      align-items: flex-end;
      flex-wrap: wrap;
      margin-bottom: 24px;
    }

    .book-select {
      flex: 2;
      min-width: 300px;
    }

    .location-select {
      flex: 1;
      min-width: 120px;
    }

    .book-option {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 4px 0;
    }

    .book-option small {
      color: rgba(0, 0, 0, 0.6);
    }





    .locations-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      margin-bottom: 24px;
    }

    .location-section h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
    }

    .location-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    /* Custom chip styles */
    .category-chip, .available-chip, .location-chip {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      margin: 2px;
    }

    .category-chip {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .available-chip {
      background-color: #f3e5f5;
      color: #7b1fa2;
    }

    .location-chip.used {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .location-chip.available {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .capacity-info {
      border-top: 1px solid #e0e0e0;
      padding-top: 20px;
    }

    .capacity-stats {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;
    }

    .capacity-stat {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .capacity-stat .label {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
    }

    .capacity-stat .value {
      font-weight: 600;
      font-size: 16px;
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: rgba(0, 0, 0, 0.6);
    }

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      opacity: 0.5;
    }



    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    @media (max-width: 768px) {
      .add-book-form {
        flex-direction: column;
        align-items: stretch;
      }

      .book-select,
      .location-select {
        flex: none;
        min-width: auto;
      }

      .locations-grid {
        grid-template-columns: 1fr;
      }

      .shelf-stats {
        flex-direction: column;
        gap: 12px;
      }
    }
  `]
})
export class ShelfBooksComponent implements OnInit {
  shelf?: Shelf;
  booksInShelf: BookInShelf[] = [];
  booksInStorage: Book[] = [];
  shelfLocations?: ShelfLocations;
  availableLocations: string[] = [];
  
  selectedBookId?: number;
  selectedLocationCode?: string;
  
  isLoading = false;
  shelfId!: number;

  displayedColumns = ['title', 'category', 'location', 'actions'];


  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private shelfService: ShelfService,
    private bookService: BookService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.shelfId = parseInt(this.route.snapshot.paramMap.get('id') || '0');
    if (this.shelfId) {
      this.loadData();
    } else {
      this.router.navigate(['/shelves']);
    }
  }

  private loadData(): void {
    this.loadShelfData();
    this.loadBooksInShelf();
    this.loadBooksInStorage();
    this.loadShelfLocations();
  }

  private loadShelfData(): void {
    this.shelfService.getShelfById(this.shelfId).subscribe({
      next: (shelf) => {
        this.shelf = shelf;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải thông tin kệ: ' + error.message, 'Đóng', {
          duration: 5000
        });
        this.router.navigate(['/shelves']);
      }
    });
  }

  private loadBooksInShelf(): void {
    this.shelfService.getBooksInShelf(this.shelfId).subscribe({
      next: (books) => {
        this.booksInShelf = books;
      },
      error: (error) => {
        console.error('Error loading books in shelf:', error);
      }
    });
  }

  private loadBooksInStorage(): void {
    this.shelfService.getBooksInStorage().subscribe({
      next: (books) => {
        this.booksInStorage = books;
      },
      error: (error) => {
        console.error('Error loading books in storage:', error);
      }
    });
  }

  private loadShelfLocations(): void {
    this.shelfService.getAvailableLocations(this.shelfId).subscribe({
      next: (locations) => {
        this.shelfLocations = locations;
        this.availableLocations = locations.availableLocations;
      },
      error: (error) => {
        console.error('Error loading shelf locations:', error);
      }
    });
  }

  canAssignBook(): boolean {
    return !!this.selectedBookId;
  }



  assignBookToShelf(): void {
    if (!this.canAssignBook() || !this.selectedBookId) return;

    this.isLoading = true;
    const assignData: AssignBookToShelf = {
      shelfId: this.shelfId,
      bookId: this.selectedBookId,
      quantity: 1, // Luôn thêm 1 quyển
      locationCode: this.selectedLocationCode || undefined
    };

    this.shelfService.assignBookToShelf(assignData).subscribe({
      next: (response) => {
        this.snackBar.open('Thêm sách vào kệ thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.resetForm();
        this.loadData(); // Reload all data
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi thêm sách: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
      }
    });
  }



  removeBookFromShelf(book: any): void {
    this.isLoading = true;

    // Sử dụng BookLocationId thay vì BookId để xóa từng vị trí riêng biệt
    this.shelfService.removeBookLocation(book.bookLocationId).subscribe({
      next: (response) => {
        this.snackBar.open('Xóa sách khỏi vị trí thành công!', 'Đóng', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        this.loadData(); // Reload all data
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi xóa sách: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
      }
    });
  }

  private resetForm(): void {
    this.selectedBookId = undefined;
    this.selectedLocationCode = undefined;
  }

  onLocationChange(): void {
    // Không cần logic đặc biệt cho location change
  }

  isLocationSelected(): boolean {
    return !!(this.selectedLocationCode && this.selectedLocationCode !== '');
  }

  goBack(): void {
    this.router.navigate(['/shelves']);
  }
}
