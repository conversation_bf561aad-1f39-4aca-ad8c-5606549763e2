import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Router, ActivatedRoute } from "@angular/router";
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
  AbstractControl,
} from "@angular/forms";
import { MatCardModule } from "@angular/material/card";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatButtonModule } from "@angular/material/button";
import { MatSelectModule } from "@angular/material/select";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatIconModule } from "@angular/material/icon";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { MatProgressBarModule } from "@angular/material/progress-bar";
import { MatDividerModule } from "@angular/material/divider";
import {
  UserManagement,
  CreateUser,
  UpdateUser,
  USER_ROLE_OPTIONS,
  UserRole,
} from "../../models/user.model";
import { UserService } from "../../services/user.service";
import { PageContainerComponent } from "../../shared/page-container/page-container.component";
import { FormContainerComponent, FormAction } from "../../shared/form-container/form-container.component";

@Component({
  selector: "app-user-form",
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatCheckboxModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressBarModule,
    MatDividerModule,
    PageContainerComponent,
    FormContainerComponent,
  ],
  template: `
    <app-page-container>
      <app-form-container
        [title]="isEditMode ? 'Chỉnh sửa người dùng' : 'Thêm người dùng mới'"
        [subtitle]="isEditMode ? 'Cập nhật thông tin người dùng' : 'Thêm người dùng mới vào hệ thống'"
        [icon]="isEditMode ? 'edit' : 'person_add'"
        [isLoading]="isLoading"
        [showCloseButton]="true"
        [actions]="formActions"
        (close)="onCancel()">

        <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="user-form">
          <!-- Personal Information -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Thông tin cá nhân</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label
                    >Tên đăng nhập <span class="required">*</span></mat-label
                  >
                  <input
                    matInput
                    formControlName="username"
                    placeholder="Nhập tên đăng nhập..."
                  />
                  <mat-error
                    *ngIf="userForm.get('username')?.hasError('required')"
                  >
                    Tên đăng nhập là bắt buộc
                  </mat-error>
                  <mat-error
                    *ngIf="userForm.get('username')?.hasError('minlength')"
                  >
                    Tên đăng nhập phải có ít nhất 3 ký tự
                  </mat-error>
                  <mat-error
                    *ngIf="userForm.get('username')?.hasError('pattern')"
                  >
                    Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Email <span class="required">*</span></mat-label>
                  <input
                    matInput
                    type="email"
                    formControlName="email"
                    placeholder="Nhập email..."
                  />
                  <mat-error
                    *ngIf="userForm.get('email')?.hasError('required')"
                  >
                    Email là bắt buộc
                  </mat-error>
                  <mat-error *ngIf="userForm.get('email')?.hasError('email')">
                    Email không đúng định dạng
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Họ <span class="required">*</span></mat-label>
                  <input
                    matInput
                    formControlName="firstName"
                    placeholder="Nhập họ..."
                  />
                  <mat-error
                    *ngIf="userForm.get('firstName')?.hasError('required')"
                  >
                    Họ là bắt buộc
                  </mat-error>
                  <mat-error
                    *ngIf="userForm.get('firstName')?.hasError('minlength')"
                  >
                    Họ phải có ít nhất 2 ký tự
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Tên <span class="required">*</span></mat-label>
                  <input
                    matInput
                    formControlName="lastName"
                    placeholder="Nhập tên..."
                  />
                  <mat-error
                    *ngIf="userForm.get('lastName')?.hasError('required')"
                  >
                    Tên là bắt buộc
                  </mat-error>
                  <mat-error
                    *ngIf="userForm.get('lastName')?.hasError('minlength')"
                  >
                    Tên phải có ít nhất 2 ký tự
                  </mat-error>
                </mat-form-field>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Password Section (Only for new users) -->
          <mat-card class="form-section" *ngIf="!isEditMode">
            <mat-card-header>
              <mat-card-title>Mật khẩu</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label
                    >Mật khẩu <span class="required">*</span></mat-label
                  >
                  <input
                    matInput
                    [type]="hidePassword ? 'password' : 'text'"
                    formControlName="password"
                    placeholder="Nhập mật khẩu..."
                  />
                  <button
                    mat-icon-button
                    matSuffix
                    (click)="hidePassword = !hidePassword"
                    type="button"
                  >
                    <mat-icon>{{
                      hidePassword ? "visibility" : "visibility_off"
                    }}</mat-icon>
                  </button>
                  <mat-error
                    *ngIf="userForm.get('password')?.hasError('required')"
                  >
                    Mật khẩu là bắt buộc
                  </mat-error>
                  <mat-error
                    *ngIf="userForm.get('password')?.hasError('minlength')"
                  >
                    Mật khẩu phải có ít nhất 6 ký tự
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label
                    >Xác nhận mật khẩu
                    <span class="required">*</span></mat-label
                  >
                  <input
                    matInput
                    [type]="hideConfirmPassword ? 'password' : 'text'"
                    formControlName="confirmPassword"
                    placeholder="Nhập lại mật khẩu..."
                  />
                  <button
                    mat-icon-button
                    matSuffix
                    (click)="hideConfirmPassword = !hideConfirmPassword"
                    type="button"
                  >
                    <mat-icon>{{
                      hideConfirmPassword ? "visibility" : "visibility_off"
                    }}</mat-icon>
                  </button>
                  <mat-error
                    *ngIf="
                      userForm.get('confirmPassword')?.hasError('required')
                    "
                  >
                    Xác nhận mật khẩu là bắt buộc
                  </mat-error>
                  <mat-error
                    *ngIf="
                      userForm
                        .get('confirmPassword')
                        ?.hasError('passwordMismatch')
                    "
                  >
                    Mật khẩu xác nhận không khớp
                  </mat-error>
                </mat-form-field>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Role and Permissions -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Vai trò và Quyền hạn</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Vai trò <span class="required">*</span></mat-label>
                  <mat-select formControlName="role">
                    <mat-option
                      *ngFor="let role of roleOptions"
                      [value]="role.value"
                    >
                      <div class="role-option">
                        <span class="role-label">{{ role.label }}</span>
                        <span class="role-description">{{
                          role.description
                        }}</span>
                      </div>
                    </mat-option>
                  </mat-select>
                  <mat-error *ngIf="userForm.get('role')?.hasError('required')">
                    Vai trò là bắt buộc
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="permissions-section">
                <mat-checkbox formControlName="isActive">
                  Tài khoản hoạt động
                </mat-checkbox>
              </div>

              <!-- Email Verification Status (Edit mode only) -->
              <div class="email-verification-status" *ngIf="isEditMode">
                <div class="status-row">
                  <mat-icon
                    [class.verified]="user?.emailVerified"
                    [class.unverified]="!user?.emailVerified"
                  >
                    {{ user?.emailVerified ? "check_circle" : "error" }}
                  </mat-icon>
                  <span
                    >Email {{ user?.emailVerified ? "đã" : "chưa" }} được xác
                    thực</span
                  >
                </div>

                <div class="verification-details" *ngIf="user?.emailVerified">
                  <p>Xác thực bởi: {{ user?.emailVerifiedByName || "N/A" }}</p>
                  <p>
                    Phương thức:
                    {{
                      getVerificationMethodText(user?.emailVerificationMethod)
                    }}
                  </p>
                  <p>
                    Thời gian:
                    {{ user?.emailVerifiedAt | date : "dd/MM/yyyy HH:mm:ss" }}
                  </p>
                </div>

                <button
                  mat-raised-button
                  color="primary"
                  *ngIf="!user?.emailVerified && isAdmin"
                  (click)="forceVerifyEmail()"
                  [disabled]="isVerifying"
                >
                  <mat-icon>verified_user</mat-icon>
                  Force Verify Email
                </button>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Role Permissions Info -->
          <mat-card class="form-section info-section">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>info</mat-icon>
                Quyền hạn theo vai trò
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="permissions-info">
                <div class="permission-item">
                  <strong>Quản trị viên:</strong> Toàn quyền quản lý hệ thống,
                  người dùng, sách, thành viên
                </div>
                <div class="permission-item">
                  <strong>Thủ thư:</strong> Quản lý sách, thành viên, mượn/trả
                  sách
                </div>
                <div class="permission-item">
                  <strong>Trợ lý:</strong> Chỉ xử lý mượn/trả sách
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </form>
      </app-form-container>
    </app-page-container>
  `,
  styles: [
    `
      .user-form {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }

      .form-section {
        margin-bottom: 24px;
      }

      .info-section {
        background-color: #f8f9fa;
      }

      .form-row {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
      }

      .form-row mat-form-field {
        flex: 1;
        min-width: 200px;
      }

      .permissions-section {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: 16px;
      }

      .permissions-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .permission-item {
        padding: 8px 0;
        border-bottom: 1px solid #eee;
      }

      .permission-item:last-child {
        border-bottom: none;
      }

      .role-option {
        display: flex;
        flex-direction: column;
      }

      .role-label {
        font-weight: 500;
      }

      .role-description {
        font-size: 0.85em;
        color: #666;
        margin-top: 2px;
      }

      .required {
        color: #f44336;
      }

      mat-card-header {
        margin-bottom: 16px;
      }

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      mat-card-actions {
        padding: 16px 24px;
        gap: 8px;
      }

      mat-progress-bar {
        margin-bottom: 16px;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .form-row {
          flex-direction: column;
        }

        .form-row mat-form-field {
          min-width: 100%;
        }
      }
    `,
  ],
})
export class UserFormComponent implements OnInit {
  userForm: FormGroup;
  isEditMode = false;
  isLoading = false;
  isSubmitting = false;
  userId?: number;
  hidePassword = true;
  hideConfirmPassword = true;
  roleOptions = USER_ROLE_OPTIONS;
  user?: UserManagement;
  isAdmin = false;
  isVerifying = false;
  formActions: FormAction[] = [];

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.userForm = this.createForm();
    this.isAdmin = this.checkIsAdmin();
    this.initializeFormActions();
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params["id"]) {
        this.isEditMode = true;
        this.userId = +params["id"];
        this.loadUser();
        this.updateFormForEdit();
      }
    });
  }

  createForm(): FormGroup {
    return this.fb.group(
      {
        username: ["", [Validators.required, Validators.minLength(3)]],
        email: ["", [Validators.required, Validators.email]],
        firstName: ["", [Validators.required, Validators.minLength(2)]],
        lastName: ["", [Validators.required, Validators.minLength(2)]],
        password: [
          "",
          this.isEditMode ? [] : [Validators.required, Validators.minLength(6)],
        ],
        confirmPassword: ["", this.isEditMode ? [] : [Validators.required]],
        role: ["", Validators.required],
        isActive: [true],
      },
      {
        validators: this.isEditMode ? [] : [this.passwordMatchValidator],
      }
    );
  }

  updateFormForEdit(): void {
    // Remove password validation for edit mode
    this.userForm.get("password")?.clearValidators();
    this.userForm.get("confirmPassword")?.clearValidators();
    this.userForm.get("password")?.updateValueAndValidity();
    this.userForm.get("confirmPassword")?.updateValueAndValidity();
  }

  passwordMatchValidator(
    control: AbstractControl
  ): { [key: string]: any } | null {
    const password = control.get("password");
    const confirmPassword = control.get("confirmPassword");

    if (
      password &&
      confirmPassword &&
      password.value !== confirmPassword.value
    ) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  loadUser(): void {
    if (!this.userId) return;

    this.isLoading = true;
    this.userService.getUser(this.userId).subscribe({
      next: (user) => {
        this.user = user;
        this.userForm.patchValue({
          username: user.username,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          isActive: user.isActive,
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open("Không thể tải thông tin người dùng", "Đóng", {
          duration: 3000,
        });
        this.isLoading = false;
        this.router.navigate(["/users"]);
      },
    });
  }

  onSubmit(): void {
    if (this.userForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    const formValue = this.userForm.value;

    if (this.isEditMode) {
      const updateUserDto: UpdateUser = {
        username: formValue.username,
        email: formValue.email,
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        role: formValue.role,
        isActive: formValue.isActive,
      };

      this.userService.updateUser(this.userId!, updateUserDto).subscribe({
        next: () => {
          this.snackBar.open("Cập nhật người dùng thành công", "Đóng", {
            duration: 3000,
          });
          this.router.navigate(["/users"]);
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || "Không thể cập nhật người dùng",
            "Đóng",
            { duration: 3000 }
          );
          this.isSubmitting = false;
        },
      });
    } else {
      const createUserDto: CreateUser = {
        username: formValue.username,
        email: formValue.email,
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        password: formValue.password,
        role: formValue.role,
        isActive: formValue.isActive,
      };

      this.userService.createUser(createUserDto).subscribe({
        next: () => {
          this.snackBar.open("Tạo người dùng thành công", "Đóng", {
            duration: 3000,
          });
          this.router.navigate(["/users"]);
        },
        error: (error) => {
          this.snackBar.open(
            error.error?.message || "Không thể tạo người dùng",
            "Đóng",
            { duration: 3000 }
          );
          this.isSubmitting = false;
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(["/users"]);
  }

  getVerificationMethodText(method?: string): string {
    switch (method) {
      case "Token":
        return "Xác thực qua email";
      case "AdminForce":
        return "Xác thực bởi Admin";
      default:
        return "N/A";
    }
  }

  forceVerifyEmail(): void {
    if (!this.userId || this.isVerifying) return;

    this.isVerifying = true;
    this.userService.forceVerifyEmail(this.userId).subscribe({
      next: (response) => {
        this.snackBar.open("Xác thực email thành công", "Đóng", {
          duration: 3000,
        });
        this.loadUser(); // Reload user data
        this.isVerifying = false;
      },
      error: (error) => {
        this.snackBar.open(
          error.error?.message || "Không thể xác thực email",
          "Đóng",
          { duration: 3000 }
        );
        this.isVerifying = false;
      },
    });
  }

  private checkIsAdmin(): boolean {
    const currentUser = JSON.parse(localStorage.getItem("currentUser") || "{}");
    return currentUser?.role === "Admin";
  }

  private initializeFormActions(): void {
    this.formActions = [
      {
        label: 'Hủy',
        type: 'button',
        icon: 'arrow_back',
        click: () => this.onCancel()
      },
      {
        label: this.isEditMode ? 'Cập nhật' : 'Thêm người dùng',
        type: 'submit',
        color: 'primary',
        icon: this.isEditMode ? 'save' : 'add',
        disabled: this.userForm?.invalid || this.isSubmitting
      }
    ];
  }

  onCancel(): void {
    this.goBack();
  }
}
