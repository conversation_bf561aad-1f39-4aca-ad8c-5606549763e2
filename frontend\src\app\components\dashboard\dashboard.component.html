<app-page-container>
  <!-- Header Section -->
  <app-page-header
    title="Dashboard"
    subtitle="Tổng quan hệ thống quản lý thư viện"
    icon="dashboard"
    [actions]="headerActions">
  </app-page-header>

  <!-- Quick Actions -->
  <app-quick-actions
    [actions]="quickActions"
    [isCompact]="false">
  </app-quick-actions>

  <!-- Notification Center -->
  <app-notification-center></app-notification-center>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <p class="loading-text">Đang tải dữ liệu...</p>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="error-container">
    <div class="error-content">
      <mat-icon class="error-icon">error_outline</mat-icon>
      <h3><PERSON><PERSON> lỗi xảy ra</h3>
      <p class="error-message">{{ error }}</p>
      <button type="button" mat-raised-button color="primary" (click)="loadDashboardData()">
        <mat-icon>refresh</mat-icon>
        Thử lại
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && !error && stats" class="dashboard-content fade-in-up">
    <!-- Stats Cards -->
    <div class="stats-grid">
      <mat-card class="stat-card books-card">
        <mat-card-content>
          <div class="stat-header">
            <div class="stat-icon books-icon">
              <mat-icon>menu_book</mat-icon>
            </div>
            <div class="stat-trend">
              <mat-icon class="trend-up">trending_up</mat-icon>
            </div>
          </div>
          <div class="stat-content">
            <h2 class="stat-number">{{ stats.totalBooks | number }}</h2>
            <p class="stat-label">Tổng số sách</p>
            <div class="stat-detail">
              <span class="detail-text">{{ stats.totalCategories }} thể loại</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card members-card">
        <mat-card-content>
          <div class="stat-header">
            <div class="stat-icon members-icon">
              <mat-icon>people</mat-icon>
            </div>
            <div class="stat-trend">
              <mat-icon class="trend-up">trending_up</mat-icon>
            </div>
          </div>
          <div class="stat-content">
            <h2 class="stat-number">{{ stats.totalMembers | number }}</h2>
            <p class="stat-label">Thành viên</p>
            <div class="stat-detail">
              <span class="detail-text">Hoạt động</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card borrows-card">
        <mat-card-content>
          <div class="stat-header">
            <div class="stat-icon borrows-icon">
              <mat-icon>assignment_turned_in</mat-icon>
            </div>
            <div class="stat-trend">
              <mat-icon class="trend-neutral">trending_flat</mat-icon>
            </div>
          </div>
          <div class="stat-content">
            <h2 class="stat-number">{{ stats.activeBorrows | number }}</h2>
            <p class="stat-label">Đang mượn</p>
            <div class="stat-detail">
              <span class="detail-text">{{ stats.totalBorrows }} tổng lượt</span>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-icon">
            <mat-icon>warning</mat-icon>
          </div>
          <div class="stat-info">
            <h2>{{ stats.overdueBooks }}</h2>
            <p>Quá hạn</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <app-data-visualization
        title="Thống kê Mượn/Trả theo Tháng"
        [chartData]="monthlyChartData"
        chartType="bar">
      </app-data-visualization>

      <app-data-visualization
        title="Phân bố Sách theo Thể loại"
        [chartData]="categoryChartData"
        chartType="pie">
      </app-data-visualization>

      <app-data-visualization
        title="Xu hướng Hoạt động Thư viện"
        [chartData]="activityChartData"
        chartType="line">
      </app-data-visualization>
    </div>

    <!-- Popular Books and Active Members -->
    <div class="tables-section">
      <mat-card class="table-card">
        <mat-card-header>
          <mat-card-title>Sách phổ biến nhất</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <table mat-table [dataSource]="popularBooks" class="mat-elevation-z0">
            <!-- Title Column -->
            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef>Tiêu đề</th>
              <td mat-cell *matCellDef="let book">
                <div class="book-info">
                  <img *ngIf="book.imageUrl" [src]="book.imageUrl" alt="{{ book.title }}" class="book-thumbnail">
                  <div>
                    <div class="book-title">{{ book.title }}</div>
                    <div class="book-author">{{ book.author }}</div>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Category Column -->
            <ng-container matColumnDef="category">
              <th mat-header-cell *matHeaderCellDef>Danh mục</th>
              <td mat-cell *matCellDef="let book">{{ book.categoryName }}</td>
            </ng-container>

            <!-- Borrow Count Column -->
            <ng-container matColumnDef="borrowCount">
              <th mat-header-cell *matHeaderCellDef>Số lượt mượn</th>
              <td mat-cell *matCellDef="let book">{{ book.borrowCount }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['title', 'category', 'borrowCount']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['title', 'category', 'borrowCount'];"></tr>
          </table>
        </mat-card-content>
      </mat-card>

      <mat-card class="table-card">
        <mat-card-header>
          <mat-card-title>Thành viên tích cực nhất</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <table mat-table [dataSource]="activeMembers" class="mat-elevation-z0">
            <!-- Name Column -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>Tên</th>
              <td mat-cell *matCellDef="let member">{{ member.fullName }}</td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Trạng thái</th>
              <td mat-cell *matCellDef="let member">
                <span [ngClass]="getMemberStatusClass(member.status)">
                  {{ getMemberStatusText(member.status) }}
                </span>
              </td>
            </ng-container>

            <!-- Borrow Count Column -->
            <ng-container matColumnDef="borrowCount">
              <th mat-header-cell *matHeaderCellDef>Số lượt mượn</th>
              <td mat-cell *matCellDef="let member">{{ member.borrowCount }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['name', 'status', 'borrowCount']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['name', 'status', 'borrowCount'];"></tr>
          </table>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Reports Section -->
    <div class="reports-section" *ngIf="canViewReports()">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Báo cáo</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="reports-grid">
            <a routerLink="/reports/overdue-books" class="report-link">
              <mat-icon>warning</mat-icon>
              <span>Sách đang quá hạn</span>
            </a>
            <a routerLink="/reports/fine-collection" class="report-link">
              <mat-icon>payments</mat-icon>
              <span>Thu phí phạt</span>
            </a>
            <a routerLink="/reports/custom" class="report-link">
              <mat-icon>analytics</mat-icon>
              <span>Báo cáo tùy chỉnh</span>
            </a>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</app-page-container>