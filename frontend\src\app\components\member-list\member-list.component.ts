import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule } from '@angular/forms';
import { Member, MemberStatus, MEMBER_STATUS_OPTIONS } from '../../models/member.model';
import { MemberService } from '../../services/member.service';
import { AuthService } from '../../services/auth.service';
import { MemberBorrowHistoryDialogComponent } from './member-borrow-history-dialog.component';
import { PageContainerComponent } from "../../shared/page-container/page-container.component";
import { PageHeaderComponent, PageHeaderAction } from "../../shared/page-header/page-header.component";
import { TableContainerComponent, SearchFilter } from "../../shared/table-container/table-container.component";

@Component({
  selector: 'app-member-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSnackBarModule,
    MatChipsModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule,
    PageContainerComponent,
    PageHeaderComponent,
    TableContainerComponent,
  ],
  template: `
    <app-page-container>
      <!-- Header Section -->
      <app-page-header
        title="Quản lý Thành viên"
        subtitle="Danh sách và quản lý tất cả thành viên thư viện"
        icon="people"
        [actions]="headerActions">
      </app-page-header>

      <!-- Table Container with Search and Data -->
      <app-table-container
        [showSearch]="true"
        searchPlaceholder="Tìm kiếm thành viên theo tên, email, số điện thoại..."
        [searchValue]="searchTerm"
        [filters]="searchFilters"
        [isLoading]="isLoading"
        [isEmpty]="members.length === 0"
        loadingMessage="Đang tải danh sách thành viên..."
        emptyIcon="people"
        emptyTitle="Chưa có thành viên nào"
        emptyMessage="Thư viện chưa có thành viên nào. Hãy thêm thành viên đầu tiên để bắt đầu."
        emptyActionText="Thêm thành viên mới"
        emptyActionIcon="person_add"
        (searchChange)="onSearchChange($event)"
        (filterChange)="onFilterChange($event)"
        (emptyAction)="navigateToAdd()">

        <!-- Members Table -->
        <table mat-table [dataSource]="members" class="members-table">
              <ng-container matColumnDef="stt">
                <th mat-header-cell *matHeaderCellDef>STT</th>
                <td mat-cell *matCellDef="let member; let i = index">{{ i + 1 }}</td>
              </ng-container>
              
              <ng-container matColumnDef="fullName">
                <th mat-header-cell *matHeaderCellDef>Họ và tên</th>
                <td mat-cell *matCellDef="let member">{{member.fullName}}</td>
              </ng-container>

              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let member">{{member.email}}</td>
              </ng-container>

              <ng-container matColumnDef="phone">
                <th mat-header-cell *matHeaderCellDef>Điện thoại</th>
                <td mat-cell *matCellDef="let member">{{member.phone || 'Chưa có'}}</td>
              </ng-container>

              <ng-container matColumnDef="membershipDate">
                <th mat-header-cell *matHeaderCellDef>Ngày tham gia</th>
                <td mat-cell *matCellDef="let member">{{formatDate(member.membershipDate)}}</td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Trạng thái</th>
                <td mat-cell *matCellDef="let member">
                  <mat-chip-set>
                    <mat-chip [class]="getStatusClass(member.status)">
                      {{member.statusName}}
                    </mat-chip>
                  </mat-chip-set>
                </td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Thao tác</th>
                               <td mat-cell *matCellDef="let member">
                   <button *ngIf="canHandleBorrowOperations()" mat-icon-button (click)="viewBorrowHistory(member)" 
                           matTooltip="Xem lịch sử mượn sách" color="accent">
                     <mat-icon>history</mat-icon>
                   </button>
                   <button *ngIf="canManageMembers()" mat-icon-button [routerLink]="['/members/edit', member.id]" 
                           matTooltip="Chỉnh sửa" color="primary">
                     <mat-icon>edit</mat-icon>
                   </button>
                   <button *ngIf="canManageMembers()" mat-icon-button (click)="deleteMember(member)" 
                           matTooltip="Xóa" color="warn">
                     <mat-icon>delete</mat-icon>
                   </button>
                 </td>
              </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </app-table-container>
    </app-page-container>
  `,
  styles: [`
    .members-table {
      width: 100%;
    }



    .status-active {
      background-color: #4caf50 !important;
      color: white;
    }

    .status-suspended {
      background-color: #ff9800 !important;
      color: white;
    }

    .status-expired {
      background-color: #f44336 !important;
      color: white;
    }

    .status-banned {
      background-color: #9c27b0 !important;
      color: white;
    }

    mat-chip-set {
      display: flex;
    }
  `]
})
export class MemberListComponent implements OnInit {
  members: Member[] = [];
  displayedColumns: string[] = ['stt', 'fullName', 'email', 'phone', 'membershipDate', 'status', 'actions'];

  // Search filters
  searchTerm: string = '';
  searchStatus?: MemberStatus;

  statusOptions = MEMBER_STATUS_OPTIONS;
  headerActions: PageHeaderAction[] = [];
  searchFilters: SearchFilter[] = [];
  isLoading = false;

  constructor(
    private memberService: MemberService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router
  ) {
    this.initializeHeaderActions();
    this.initializeSearchFilters();
  }

  ngOnInit(): void {
    this.loadMembers();
  }

  canManageMembers(): boolean {
    return this.authService.canManageMembers();
  }

  canHandleBorrowOperations(): boolean {
    return this.authService.canHandleBorrowOperations();
  }

  private initializeHeaderActions(): void {
    this.headerActions = [];
    if (this.canManageMembers()) {
      this.headerActions.push({
        label: 'Thêm thành viên mới',
        icon: 'person_add',
        color: 'primary',
        routerLink: '/members/add'
      });
    }
  }

  private initializeSearchFilters(): void {
    this.searchFilters = [
      {
        key: 'status',
        label: 'Trạng thái',
        type: 'select',
        options: this.statusOptions.map(status => ({
          value: status.value,
          label: status.label
        })),
        value: this.searchStatus
      }
    ];
  }

  onSearchChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.onSearch();
  }

  onFilterChange(filters: SearchFilter[]): void {
    const statusFilter = filters.find(f => f.key === 'status');
    this.searchStatus = statusFilter?.value;
    this.onSearch();
  }

  navigateToAdd(): void {
    this.router.navigate(['/members/add']);
  }

  loadMembers(): void {
    this.isLoading = true;
    this.memberService.getMembers().subscribe({
      next: (members) => {
        this.members = members;
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải danh sách thành viên', 'Đóng', {
          duration: 3000
        });
        this.isLoading = false;
      }
    });
  }

  onSearch(): void {
    if (this.hasActiveFilters()) {
      this.isLoading = true;
      this.memberService.searchMembers(
        this.searchTerm || undefined,
        undefined,
        undefined,
        undefined,
        this.searchStatus
      ).subscribe({
        next: (members) => {
          this.members = members;
          this.isLoading = false;
        },
        error: (error) => {
          this.snackBar.open('Lỗi khi tìm kiếm thành viên', 'Đóng', {
            duration: 3000
          });
          this.isLoading = false;
        }
      });
    } else {
      this.loadMembers();
    }
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.searchStatus = undefined;
    this.loadMembers();
  }

  hasActiveFilters(): boolean {
    return !!(this.searchTerm || this.searchStatus !== undefined);
  }

  deleteMember(member: Member): void {
    if (confirm(`Bạn có chắc chắn muốn xóa thành viên "${member.fullName}"?`)) {
      this.memberService.deleteMember(member.id).subscribe({
        next: () => {
          this.snackBar.open('Xóa thành viên thành công', 'Đóng', {
            duration: 3000
          });
          this.loadMembers();
        },
        error: (error) => {
          // Handle different error response formats from backend
          let message = 'Lỗi khi xóa thành viên';
          
          if (error.error) {
            // If error.error is a string (from BadRequest(string))
            if (typeof error.error === 'string') {
              message = error.error;
            }
            // If error.error is an object with message property
            else if (error.error.message) {
              message = error.error.message;
            }
            // If error.error is an object with title property (ValidationProblem)
            else if (error.error.title) {
              message = error.error.title;
            }
          }
          // Fallback to error.message
          else if (error.message) {
            message = error.message;
          }
          
          this.snackBar.open(message, 'Đóng', {
            duration: 5000
          });
        }
      });
    }
  }

  viewBorrowHistory(member: Member): void {
    this.dialog.open(MemberBorrowHistoryDialogComponent, {
      width: '900px',
      maxWidth: '90vw',
      data: { member }
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  }

  getStatusClass(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.Active:
        return 'status-active';
      case MemberStatus.Suspended:
        return 'status-suspended';
      case MemberStatus.Expired:
        return 'status-expired';
      case MemberStatus.Banned:
        return 'status-banned';
      default:
        return '';
    }
  }
} 