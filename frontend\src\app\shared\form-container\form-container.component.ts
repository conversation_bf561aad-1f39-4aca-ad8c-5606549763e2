import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDividerModule } from '@angular/material/divider';

export interface FormAction {
  label: string;
  type: 'submit' | 'button';
  color?: 'primary' | 'accent' | 'warn';
  icon?: string;
  disabled?: boolean;
  click?: () => void;
}

@Component({
  selector: 'app-form-container',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatDividerModule
  ],
  template: `
    <div class="form-container">
      <mat-card class="form-card">
        <!-- Form Header -->
        <mat-card-header class="form-header">
          <div class="header-content">
            <div class="header-icon" *ngIf="icon">
              <mat-icon [class]="iconClass">{{ icon }}</mat-icon>
            </div>
            <div class="header-text">
              <mat-card-title>{{ title }}</mat-card-title>
              <mat-card-subtitle *ngIf="subtitle">{{ subtitle }}</mat-card-subtitle>
            </div>
          </div>
          <button 
            *ngIf="showCloseButton"
            mat-icon-button 
            (click)="onClose()"
            class="close-button">
            <mat-icon>close</mat-icon>
          </button>
        </mat-card-header>

        <!-- Progress Bar -->
        <mat-progress-bar 
          *ngIf="isLoading" 
          mode="indeterminate"
          class="form-progress">
        </mat-progress-bar>

        <!-- Form Content -->
        <mat-card-content class="form-content">
          <ng-content></ng-content>
        </mat-card-content>

        <!-- Form Actions -->
        <mat-card-actions 
          *ngIf="actions.length > 0" 
          class="form-actions"
          align="end">
          <mat-divider></mat-divider>
          <div class="actions-wrapper">
            <ng-container *ngFor="let action of actions">
              <button
                mat-raised-button
                [type]="action.type"
                [color]="action.color || 'primary'"
                [disabled]="action.disabled || isLoading"
                (click)="action.click && action.click()"
                class="action-button">
                <mat-icon *ngIf="action.icon">{{ action.icon }}</mat-icon>
                {{ action.label }}
              </button>
            </ng-container>
          </div>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .form-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 0;
    }

    .form-card {
      border-radius: 16px;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--color-outline);
      overflow: hidden;
      background: var(--color-surface);
    }

    .form-header {
      background: var(--color-surface-variant);
      padding: 24px 32px;
      border-bottom: 1px solid var(--color-outline);
    }

    .form-header .mat-mdc-card-header-text {
      margin: 0;
      width: 100%;
    }

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
    }

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: var(--color-primary);
      color: var(--color-on-primary);
      flex-shrink: 0;
    }

    .header-icon mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .header-icon.secondary {
      background: var(--color-secondary);
    }

    .header-icon.success {
      background: var(--color-success);
    }

    .header-icon.warning {
      background: var(--color-warning);
    }

    .header-text {
      flex: 1;
    }

    .form-header .mat-mdc-card-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--color-on-surface);
      margin: 0;
      line-height: 1.2;
    }

    .form-header .mat-mdc-card-subtitle {
      font-size: 16px;
      color: var(--color-on-surface);
      opacity: 0.7;
      margin: 4px 0 0 0;
      line-height: 1.4;
    }

    .close-button {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      margin-left: 16px;
    }

    .form-progress {
      height: 3px;
    }

    .form-content {
      padding: 32px;
    }

    .form-content ::ng-deep .form-section {
      margin-bottom: 32px;
    }

    .form-content ::ng-deep .form-section:last-child {
      margin-bottom: 0;
    }

    .form-content ::ng-deep .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 20px;
    }

    .form-content ::ng-deep .form-row.single {
      grid-template-columns: 1fr;
    }

    .form-content ::ng-deep .form-group {
      margin-bottom: 20px;
    }

    .form-content ::ng-deep .mat-mdc-form-field {
      width: 100%;
    }

    .form-content ::ng-deep .required {
      color: var(--color-error);
    }

    .form-actions {
      padding: 0;
      margin: 0;
    }

    .form-actions mat-divider {
      margin-bottom: 0;
    }

    .actions-wrapper {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      padding: 20px 32px;
      background: var(--color-surface-variant);
    }

    .action-button {
      border-radius: 8px;
      font-weight: 500;
      text-transform: none;
      padding: 0 24px;
      height: 40px;
    }

    .action-button mat-icon {
      margin-right: 8px;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .form-container {
        max-width: 100%;
        margin: 0;
      }

      .form-header {
        padding: 20px 24px;
      }

      .header-content {
        gap: 12px;
      }

      .header-icon {
        width: 40px;
        height: 40px;
      }

      .header-icon mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .form-header .mat-mdc-card-title {
        font-size: 20px;
      }

      .form-header .mat-mdc-card-subtitle {
        font-size: 14px;
      }

      .form-content {
        padding: 24px;
      }

      .form-content ::ng-deep .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
      }

      .actions-wrapper {
        padding: 16px 24px;
        flex-direction: column-reverse;
      }

      .action-button {
        width: 100%;
      }
    }

    @media (max-width: 480px) {
      .form-header {
        padding: 16px 20px;
      }

      .form-content {
        padding: 20px;
      }

      .actions-wrapper {
        padding: 16px 20px;
      }
    }

    /* Animation */
    .form-card {
      animation: slideInUp 0.4s ease-out;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `]
})
export class FormContainerComponent {
  @Input() title: string = '';
  @Input() subtitle?: string;
  @Input() icon?: string;
  @Input() iconClass?: string = '';
  @Input() isLoading: boolean = false;
  @Input() showCloseButton: boolean = false;
  @Input() actions: FormAction[] = [];

  @Output() close = new EventEmitter<void>();

  onClose(): void {
    this.close.emit();
  }
}
