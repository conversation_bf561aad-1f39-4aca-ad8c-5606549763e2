namespace LibraryManagement.Core.Entities;

public class BookLocation : BaseEntity
{
    public int BookId { get; set; }
    public int BookshelfId { get; set; }
    public string? LocationCode { get; set; } // A1, B2, etc.
    public int Quantity { get; set; } = 1; // <PERSON>ố lượng tại vị trí này (thường là 1)
    
    // Navigation properties
    public virtual Book Book { get; set; } = null!;
    public virtual Bookshelf Bookshelf { get; set; } = null!;
}
