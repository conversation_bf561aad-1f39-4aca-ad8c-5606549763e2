import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-page-container',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="page-container" [class.full-width]="fullWidth" [class.no-padding]="noPadding">
      <ng-content></ng-content>
    </div>
  `,
  styles: [`
    .page-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 24px 32px;
      min-height: calc(100vh - 200px);
      background: transparent;
    }

    .page-container.full-width {
      max-width: none;
      padding: 24px 16px;
    }

    .page-container.no-padding {
      padding: 0;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .page-container {
        max-width: 100%;
        padding: 20px 24px;
      }
    }

    @media (max-width: 768px) {
      .page-container {
        padding: 16px 20px;
      }

      .page-container.full-width {
        padding: 16px 12px;
      }
    }

    @media (max-width: 480px) {
      .page-container {
        padding: 12px 16px;
      }

      .page-container.full-width {
        padding: 12px 8px;
      }
    }

    /* Animation */
    .page-container {
      animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `]
})
export class PageContainerComponent {
  @Input() fullWidth: boolean = false;
  @Input() noPadding: boolean = false;
}
