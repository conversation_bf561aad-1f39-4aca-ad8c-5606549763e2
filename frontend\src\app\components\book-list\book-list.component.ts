import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { MatTableModule } from "@angular/material/table";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatCardModule } from "@angular/material/card";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { MatDialogModule, MatDialog } from "@angular/material/dialog";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatMenuModule } from "@angular/material/menu";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatDividerModule } from "@angular/material/divider";
import { MatChipsModule } from "@angular/material/chips";
import { MatBadgeModule } from "@angular/material/badge";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { FormsModule } from "@angular/forms";
import { SelectionModel } from "@angular/cdk/collections";
import { Book } from "../../models/book.model";
import { BookService } from "../../services/book.service";
import { AuthService } from "../../services/auth.service";
import { ThemeService } from "../../services/theme.service";

@Component({
  selector: "app-book-list",
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatInputModule,
    MatFormFieldModule,
    MatSnackBarModule,
    MatDialogModule,
    MatCheckboxModule,
    MatMenuModule,
    MatTooltipModule,
    MatDividerModule,
    MatChipsModule,
    MatBadgeModule,
    MatProgressSpinnerModule,
    FormsModule,
  ],
  template: `
    <div class="page-container">
      <!-- Page Header -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-icon">
            <mat-icon>menu_book</mat-icon>
          </div>
          <div class="header-text">
            <h1>Quản lý Sách</h1>
            <p>Danh sách và quản lý tất cả sách trong thư viện</p>
          </div>
        </div>
        <div class="header-actions">
          <button
            mat-raised-button
            color="primary"
            routerLink="/books/add"
            *ngIf="canManageBooks()">
            <mat-icon>add</mat-icon>
            Thêm sách mới
          </button>
        </div>
      </div>

      <!-- Content Area -->
      <div class="content-area">
        <!-- Search Section -->
        <div class="search-section">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Tìm kiếm sách</mat-label>
            <input
              matInput
              [(ngModel)]="searchTerm"
              (input)="onSearch()"
              placeholder="Nhập tên sách, tác giả, ISBN...">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
          <button
            mat-stroked-button
            (click)="clearSearch()"
            *ngIf="searchTerm"
            class="clear-search-btn">
            <mat-icon>clear</mat-icon>
            Xóa
          </button>
        </div>

        <!-- Bulk Actions -->
        <div class="bulk-actions" *ngIf="selection.hasValue() && canManageBooks()">
          <div class="selection-info">
            <mat-icon>check_circle</mat-icon>
            <span>Đã chọn {{ selection.selected.length }} sách</span>
          </div>
          <div class="bulk-actions-buttons">
            <button
              mat-raised-button
              color="warn"
              (click)="deleteSelectedBooks()"
              [disabled]="isDeleting">
              <mat-icon>delete</mat-icon>
              Xóa đã chọn
            </button>
            <button
              mat-stroked-button
              (click)="selection.clear()">
              <mat-icon>clear</mat-icon>
              Bỏ chọn
            </button>
          </div>
        </div>

        <!-- Loading State -->
        <div class="loading-container" *ngIf="isLoading">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Đang tải danh sách sách...</p>
        </div>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="!isLoading && books.length === 0 && !searchTerm">
          <mat-icon>menu_book</mat-icon>
          <h3>Chưa có sách nào</h3>
          <p>Thư viện chưa có sách nào. Hãy thêm sách đầu tiên để bắt đầu.</p>
          <button
            mat-raised-button
            color="primary"
            routerLink="/books/add"
            *ngIf="canManageBooks()">
            <mat-icon>add</mat-icon>
            Thêm sách mới
          </button>
        </div>

        <!-- No Search Results -->
        <div class="empty-state" *ngIf="!isLoading && books.length === 0 && searchTerm">
          <mat-icon>search_off</mat-icon>
          <h3>Không tìm thấy kết quả</h3>
          <p>Không tìm thấy sách nào với từ khóa: "{{ searchTerm }}"</p>
          <button mat-stroked-button (click)="clearSearch()">
            <mat-icon>clear</mat-icon>
            Xóa tìm kiếm
          </button>
        </div>

        <!-- Books Table -->
        <div class="table-container" *ngIf="!isLoading && books.length > 0">
          <table mat-table [dataSource]="books" class="books-table">
            <!-- Checkbox Column -->
            <ng-container matColumnDef="select" *ngIf="canManageBooks()">
              <th mat-header-cell *matHeaderCellDef>
                <mat-checkbox
                  (change)="$event ? masterToggle() : null"
                  [checked]="selection.hasValue() && isAllSelected()"
                  [indeterminate]="selection.hasValue() && !isAllSelected()">
                </mat-checkbox>
              </th>
              <td mat-cell *matCellDef="let book">
                <mat-checkbox
                  (click)="$event.stopPropagation()"
                  (change)="$event ? selection.toggle(book) : null"
                  [checked]="selection.isSelected(book)">
                </mat-checkbox>
              </td>
            </ng-container>

            <!-- STT Column -->
            <ng-container matColumnDef="stt">
              <th mat-header-cell *matHeaderCellDef>STT</th>
              <td mat-cell *matCellDef="let book; let i = index">{{ i + 1 }}</td>
            </ng-container>

            <!-- Book Info Column -->
            <ng-container matColumnDef="title">
              <th mat-header-cell *matHeaderCellDef>Thông tin sách</th>
              <td mat-cell *matCellDef="let book">
                <div class="book-cell">
                  <div class="book-image">
                    <img *ngIf="book.imageUrl" [src]="book.imageUrl" [alt]="book.title" />
                    <mat-icon *ngIf="!book.imageUrl">menu_book</mat-icon>
                  </div>
                  <div class="book-details">
                    <h4>{{ book.title }}</h4>
                    <p>{{ book.author }}</p>
                    <div class="book-meta">
                      <mat-chip-set>
                        <mat-chip>{{ book.categoryName }}</mat-chip>
                        <mat-chip *ngIf="book.isbn">ISBN: {{ book.isbn }}</mat-chip>
                      </mat-chip-set>
                    </div>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Quantity Column -->
            <ng-container matColumnDef="quantity">
              <th mat-header-cell *matHeaderCellDef>Số lượng</th>
              <td mat-cell *matCellDef="let book">
                <div class="quantity-info">
                  <div class="quantity-item total">
                    <span>Tổng: {{ book.quantity }}</span>
                  </div>
                  <div class="quantity-item stock">
                    <span>Kho: {{ book.stockQuantity }}</span>
                  </div>
                  <div class="quantity-item shelf">
                    <span>Kệ: {{ book.onShelfQuantity }}</span>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions" *ngIf="canManageBooks()">
              <th mat-header-cell *matHeaderCellDef>Thao tác</th>
              <td mat-cell *matCellDef="let book">
                <div class="action-buttons">
                  <button
                    mat-icon-button
                    [routerLink]="['/books/edit', book.id]"
                    color="primary"
                    matTooltip="Chỉnh sửa">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    (click)="deleteBook(book)"
                    color="warn"
                    matTooltip="Xóa">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .page-container {
        min-height: 100vh;
        background: #f5f5f5;
      }

      .page-header {
        background: white;
        padding: 24px 32px;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .header-content {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .header-icon {
        width: 48px;
        height: 48px;
        background: #1976d2;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .header-icon mat-icon {
        color: white;
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .header-text h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #212121;
      }

      .header-text p {
        margin: 4px 0 0 0;
        color: #757575;
        font-size: 14px;
      }

      .content-area {
        padding: 24px 32px;
        max-width: 1200px;
        margin: 0 auto;
      }

      .search-section {
        display: flex;
        gap: 16px;
        margin-bottom: 24px;
        align-items: flex-end;
      }

      .search-field {
        flex: 1;
        max-width: 400px;
      }

      .clear-search-btn {
        height: 56px;
      }

      .bulk-actions {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .selection-info {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #1976d2;
        font-weight: 500;
      }

      .bulk-actions-buttons {
        display: flex;
        gap: 12px;
      }

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 64px;
        gap: 16px;
      }

      .loading-container p {
        color: #757575;
        margin: 0;
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 64px;
        text-align: center;
        gap: 16px;
      }

      .empty-state mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #bdbdbd;
      }

      .empty-state h3 {
        margin: 0;
        color: #424242;
        font-size: 20px;
        font-weight: 500;
      }

      .empty-state p {
        margin: 0;
        color: #757575;
        max-width: 400px;
      }

      .table-container {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }

      .books-table {
        width: 100%;
      }

      .book-cell {
        display: flex;
        gap: 16px;
        align-items: center;
        padding: 8px 0;
      }

      .book-image {
        width: 48px;
        height: 64px;
        border-radius: 4px;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        border: 1px solid #e0e0e0;
      }

      .book-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .book-image mat-icon {
        color: #bdbdbd;
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .book-details {
        flex: 1;
        min-width: 0;
      }

      .book-details h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 500;
        color: #212121;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .book-details p {
        margin: 0 0 8px 0;
        color: #757575;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .quantity-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .quantity-item {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        min-width: 60px;
      }

      .quantity-item.total {
        background: #e3f2fd;
        color: #1976d2;
      }

      .quantity-item.stock {
        background: #e8f5e8;
        color: #388e3c;
      }

      .quantity-item.shelf {
        background: #fff3e0;
        color: #f57c00;
      }

      .action-buttons {
        display: flex;
        gap: 4px;
      }

      @media (max-width: 768px) {
        .page-header {
          flex-direction: column;
          align-items: stretch;
          gap: 16px;
          padding: 16px 24px;
        }

        .content-area {
          padding: 16px 24px;
        }

        .search-section {
          flex-direction: column;
          align-items: stretch;
        }

        .bulk-actions {
          flex-direction: column;
          gap: 16px;
        }

        .book-cell {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
        }

        .book-image {
          align-self: center;
        }

        .quantity-info {
          flex-direction: row;
          justify-content: space-around;
          width: 100%;
        }
      }
    `,
  ],
})
export class BookListComponent implements OnInit {
  books: Book[] = [];
  displayedColumns: string[] = [];
  searchTerm: string = "";
  selection = new SelectionModel<Book>(true, []);
  isDeleting = false;
  isLoading = false;

  constructor(
    private bookService: BookService,
    private authService: AuthService,
    private themeService: ThemeService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.updateDisplayedColumns();
  }

  ngOnInit(): void {
    this.loadBooks();
  }

  canManageBooks(): boolean {
    return this.authService.canManageBooks();
  }

  private updateDisplayedColumns(): void {
    this.displayedColumns = this.canManageBooks()
      ? ["select", "stt", "title", "quantity", "actions"]
      : ["stt", "title", "quantity"];
  }

  loadBooks(): void {
    this.isLoading = true;
    this.bookService.getBooks().subscribe({
      next: (books) => {
        this.books = books;
        this.selection.clear();
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open("Lỗi khi tải danh sách sách", "Đóng", {
          duration: 3000,
        });
        this.isLoading = false;
      },
    });
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.loadBooks();
  }

  onSearch(): void {
    if (this.searchTerm.trim()) {
      this.isLoading = true;
      this.bookService.searchBooksGeneral(this.searchTerm.trim()).subscribe({
        next: (books) => {
          this.books = books;
          this.selection.clear();
          this.isLoading = false;
        },
        error: (error) => {
          this.snackBar.open("Lỗi khi tìm kiếm sách", "Đóng", {
            duration: 3000,
          });
          this.isLoading = false;
        },
      });
    } else {
      this.loadBooks();
    }
  }



  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.books.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected()
      ? this.selection.clear()
      : this.books.forEach(book => this.selection.select(book));
  }

  selectAll(): void {
    this.books.forEach(book => this.selection.select(book));
  }

  clearSelection(): void {
    this.selection.clear();
  }

  // Delete methods
  deleteBook(book: Book): void {
    if (confirm(`Bạn có chắc chắn muốn xóa sách "${book.title}"?`)) {
      this.bookService.deleteBook(book.id).subscribe({
        next: () => {
          this.snackBar.open("Xóa sách thành công", "Đóng", {
            duration: 3000,
          });
          this.loadBooks();
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error) {
            if (typeof error.error === "string") {
              message = error.error;
            } else if (error.error.message) {
              message = error.error.message;
            } else if (error.error.title) {
              message = error.error.title;
            }
          } else if (error.message) {
            message = error.message;
          }

          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
        },
      });
    }
  }

  deleteSelectedBooks(): void {
    const selectedBooks = this.selection.selected;
    const bookTitles = selectedBooks.map(book => book.title).join(', ');
    
    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedBooks.length} sách đã chọn?\n\n${bookTitles}`)) {
      this.isDeleting = true;
      const bookIds = selectedBooks.map(book => book.id);
      
      this.bookService.deleteMultipleBooks(bookIds).subscribe({
        next: (result) => {
          this.snackBar.open(result.message, "Đóng", {
            duration: 5000,
          });
          
          if (result.failedReasons && result.failedReasons.length > 0) {
            console.warn('Một số sách không thể xóa:', result.failedReasons);
          }
          
          this.loadBooks();
          this.isDeleting = false;
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error?.message) {
            message = error.error.message;
          }
          
          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isDeleting = false;
        },
      });
    }
  }

  deleteAllBooks(): void {
    if (this.books.length === 0) {
      this.snackBar.open("Không có sách nào để xóa", "Đóng", {
        duration: 3000,
      });
      return;
    }

    const confirmMessage = `⚠️ CẢNH BÁO: Bạn đang chuẩn bị xóa TẤT CẢ ${this.books.length} sách!\n\nHành động này không thể hoàn tác. Bạn có chắc chắn muốn tiếp tục?`;
    
    if (confirm(confirmMessage)) {
      this.isDeleting = true;
      const allBookIds = this.books.map(book => book.id);
      
      this.bookService.deleteMultipleBooks(allBookIds).subscribe({
        next: (result) => {
          this.snackBar.open(result.message, "Đóng", {
            duration: 5000,
          });
          
          if (result.failedReasons && result.failedReasons.length > 0) {
            console.warn('Một số sách không thể xóa:', result.failedReasons);
          }
          
          this.loadBooks();
          this.isDeleting = false;
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error?.message) {
            message = error.error.message;
          }
          
          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isDeleting = false;
        },
      });
    }
  }
}
