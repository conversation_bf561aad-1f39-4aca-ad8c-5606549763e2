import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Router } from "@angular/router";
import { MatTableModule } from "@angular/material/table";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatCardModule } from "@angular/material/card";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { MatDialogModule, MatDialog } from "@angular/material/dialog";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatMenuModule } from "@angular/material/menu";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatDividerModule } from "@angular/material/divider";
import { MatChipsModule } from "@angular/material/chips";
import { MatBadgeModule } from "@angular/material/badge";
import { FormsModule } from "@angular/forms";
import { SelectionModel } from "@angular/cdk/collections";
import { Book } from "../../models/book.model";
import { BookService } from "../../services/book.service";
import { AuthService } from "../../services/auth.service";
import { ThemeService } from "../../services/theme.service";
import { LoadingStateComponent } from "../../shared/loading-state/loading-state.component";
import { EmptyStateComponent } from "../../shared/empty-state/empty-state.component";
import { PageContainerComponent } from "../../shared/page-container/page-container.component";
import { PageHeaderComponent, PageHeaderAction } from "../../shared/page-header/page-header.component";
import { TableContainerComponent, SearchFilter } from "../../shared/table-container/table-container.component";

@Component({
  selector: "app-book-list",
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatInputModule,
    MatFormFieldModule,
    MatSnackBarModule,
    MatDialogModule,
    MatCheckboxModule,
    MatMenuModule,
    MatTooltipModule,
    MatDividerModule,
    MatChipsModule,
    MatBadgeModule,
    FormsModule,
    LoadingStateComponent,
    EmptyStateComponent,
    PageContainerComponent,
    PageHeaderComponent,
    TableContainerComponent,
  ],
  template: `
    <app-page-container>
      <!-- Header Section -->
      <app-page-header
        title="Quản lý Sách"
        subtitle="Danh sách và quản lý tất cả sách trong thư viện"
        icon="menu_book"
        [actions]="headerActions">
      </app-page-header>

      <!-- Table Container with Search and Data -->
      <app-table-container
        [showSearch]="true"
        searchPlaceholder="Tìm kiếm sách theo tên, tác giả, ISBN..."
        [searchValue]="searchTerm"
        [isLoading]="isLoading"
        [isEmpty]="books.length === 0"
        loadingMessage="Đang tải danh sách sách..."
        emptyIcon="menu_book"
        emptyTitle="Chưa có sách nào"
        emptyMessage="Thư viện chưa có sách nào. Hãy thêm sách đầu tiên để bắt đầu."
        emptyActionText="Thêm sách mới"
        emptyActionIcon="add"
        (searchChange)="onSearchChange($event)"
        (emptyAction)="navigateToAdd()">

        <!-- Bulk Actions -->
        <div class="bulk-actions" *ngIf="selection.hasValue() && canManageBooks()">
          <div class="selection-info">
            <mat-icon>check_circle</mat-icon>
            <span class="selected-count">
              Đã chọn {{ selection.selected.length }} sách
            </span>
          </div>
          <div class="bulk-actions-buttons">
            <button
              type="button"
              mat-raised-button
              color="warn"
              (click)="deleteSelectedBooks()"
              [disabled]="isDeleting"
              matTooltip="Xóa các sách đã chọn">
              <mat-icon>delete</mat-icon>
              Xóa đã chọn
            </button>
            <button
              type="button"
              mat-stroked-button
              (click)="selection.clear()"
              matTooltip="Bỏ chọn tất cả">
              <mat-icon>clear</mat-icon>
              Bỏ chọn
            </button>
          </div>
        </div>

        <!-- Books Table -->
        <table mat-table [dataSource]="books" class="books-table">
              <!-- Checkbox Column -->
              <ng-container matColumnDef="select" *ngIf="canManageBooks()">
                <th mat-header-cell *matHeaderCellDef>
                  <mat-checkbox
                    (change)="$event ? masterToggle() : null"
                    [checked]="selection.hasValue() && isAllSelected()"
                    [indeterminate]="selection.hasValue() && !isAllSelected()"
                    matTooltip="Chọn tất cả">
                  </mat-checkbox>
                </th>
                <td mat-cell *matCellDef="let book">
                  <mat-checkbox
                    (click)="$event.stopPropagation()"
                    (change)="$event ? selection.toggle(book) : null"
                    [checked]="selection.isSelected(book)">
                  </mat-checkbox>
                </td>
              </ng-container>

              <!-- STT Column -->
              <ng-container matColumnDef="stt">
                <th mat-header-cell *matHeaderCellDef>STT</th>
                <td mat-cell *matCellDef="let book; let i = index">{{ i + 1 }}</td>
              </ng-container>

              <!-- Book Info Column -->
              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Thông tin sách</th>
                <td mat-cell *matCellDef="let book">
                  <div class="book-cell">
                    <div class="book-image">
                      <img *ngIf="book.imageUrl" [src]="book.imageUrl" [alt]="book.title" />
                      <mat-icon *ngIf="!book.imageUrl" class="book-placeholder">menu_book</mat-icon>
                    </div>
                    <div class="book-details">
                      <h4 class="book-title">{{ book.title }}</h4>
                      <p class="book-author">{{ book.author }}</p>
                      <div class="book-meta">
                        <mat-chip-set>
                          <mat-chip>{{ book.categoryName }}</mat-chip>
                          <mat-chip *ngIf="book.isbn">ISBN: {{ book.isbn }}</mat-chip>
                        </mat-chip-set>
                      </div>
                    </div>
                  </div>
                </td>
              </ng-container>

              <!-- Quantity Column -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef>Số lượng</th>
                <td mat-cell *matCellDef="let book">
                  <div class="quantity-info">
                    <div class="quantity-item">
                      <span class="quantity-label">Tổng:</span>
                      <span class="quantity-value total">{{ book.quantity }}</span>
                    </div>
                    <div class="quantity-item">
                      <span class="quantity-label">Trong kho:</span>
                      <span class="quantity-value stock">{{ book.stockQuantity }}</span>
                    </div>
                    <div class="quantity-item">
                      <span class="quantity-label">Trên kệ:</span>
                      <span class="quantity-value shelf">{{ book.onShelfQuantity }}</span>
                    </div>
                  </div>
                </td>
              </ng-container>
              <!-- Actions Column -->
              <ng-container matColumnDef="actions" *ngIf="canManageBooks()">
                <th mat-header-cell *matHeaderCellDef>Thao tác</th>
                <td mat-cell *matCellDef="let book">
                  <div class="action-buttons">
                    <button
                      type="button"
                      mat-icon-button
                      [routerLink]="['/books/edit', book.id]"
                      color="primary"
                      matTooltip="Chỉnh sửa">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button
                      type="button"
                      mat-icon-button
                      (click)="deleteBook(book)"
                      color="warn"
                      matTooltip="Xóa">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </app-table-container>
    </app-page-container>
  `,
  styles: [
    `
      .bulk-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 24px;
        background: var(--color-surface-variant);
        border-radius: 12px;
        margin-bottom: 16px;
        border: 1px solid var(--color-outline);
      }

      .selection-info {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--color-primary);
        font-weight: 500;
      }

      .bulk-actions-buttons {
        display: flex;
        gap: 8px;
      }

      .books-table {
        width: 100%;
      }





      .books-table {
        width: 100%;
      }

      .book-cell {
        display: flex;
        gap: 16px;
        align-items: center;
        padding: 8px 0;
      }

      .book-image {
        width: 60px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        background: var(--color-surface-variant);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        border: 1px solid var(--color-outline);
      }

      .book-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }

      .book-placeholder {
        font-size: 32px;
        width: 32px;
        height: 32px;
        color: var(--color-on-surface);
        opacity: 0.5;
      }

      .book-details {
        flex: 1;
        min-width: 0;
      }

      .book-title {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--color-on-surface);
      }

      .book-author {
        margin: 0 0 8px 0;
        color: var(--color-on-surface);
        opacity: 0.8;
      }

      .quantity-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .quantity-item {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        padding: 2px 8px;
        border-radius: 4px;
        background-color: var(--color-surface-variant);
        margin-bottom: 2px;
      }

      .quantity-label {
        font-weight: 500;
        color: var(--color-on-surface);
        opacity: 0.7;
      }

      .quantity-value {
        font-weight: 600;
      }

      .quantity-value.total {
        color: var(--color-primary);
      }

      .quantity-value.stock {
        color: var(--color-success);
      }

      .quantity-value.shelf {
        color: var(--color-warning);
      }

      .action-buttons {
        display: flex;
        gap: 4px;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .bulk-actions {
          flex-direction: column;
          gap: 12px;
        }

        .bulk-actions-buttons {
          justify-content: stretch;
        }

        .bulk-actions-buttons button {
          flex: 1;
        }
      }
    `,
  ],
})
export class BookListComponent implements OnInit {
  books: Book[] = [];
  displayedColumns: string[] = [];
  searchTerm: string = "";
  selection = new SelectionModel<Book>(true, []);
  isDeleting = false;
  isLoading = false;
  headerActions: PageHeaderAction[] = [];

  constructor(
    private bookService: BookService,
    private authService: AuthService,
    private themeService: ThemeService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router
  ) {
    this.updateDisplayedColumns();
    this.initializeHeaderActions();
  }

  ngOnInit(): void {
    this.loadBooks();
  }

  canManageBooks(): boolean {
    return this.authService.canManageBooks();
  }

  private updateDisplayedColumns(): void {
    this.displayedColumns = this.canManageBooks()
      ? ["select", "stt", "title", "quantity", "actions"]
      : ["stt", "title", "quantity"];
  }

  private initializeHeaderActions(): void {
    this.headerActions = [];
    if (this.canManageBooks()) {
      this.headerActions.push({
        label: 'Thêm sách mới',
        icon: 'add',
        color: 'primary',
        routerLink: '/books/add'
      });
    }
  }

  onSearchChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.onSearch();
  }

  loadBooks(): void {
    this.isLoading = true;
    this.bookService.getBooks().subscribe({
      next: (books) => {
        this.books = books;
        this.selection.clear();
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open("Lỗi khi tải danh sách sách", "Đóng", {
          duration: 3000,
        });
        this.isLoading = false;
      },
    });
  }

  navigateToAdd(): void {
    this.router.navigate(['/books/add']);
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.loadBooks();
  }

  onSearch(): void {
    if (this.searchTerm.trim()) {
      this.isLoading = true;
      this.bookService.searchBooksGeneral(this.searchTerm.trim()).subscribe({
        next: (books) => {
          this.books = books;
          this.selection.clear();
          this.isLoading = false;
        },
        error: (error) => {
          this.snackBar.open("Lỗi khi tìm kiếm sách", "Đóng", {
            duration: 3000,
          });
          this.isLoading = false;
        },
      });
    } else {
      this.loadBooks();
    }
  }



  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.books.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected()
      ? this.selection.clear()
      : this.books.forEach(book => this.selection.select(book));
  }

  selectAll(): void {
    this.books.forEach(book => this.selection.select(book));
  }

  clearSelection(): void {
    this.selection.clear();
  }

  // Delete methods
  deleteBook(book: Book): void {
    if (confirm(`Bạn có chắc chắn muốn xóa sách "${book.title}"?`)) {
      this.bookService.deleteBook(book.id).subscribe({
        next: () => {
          this.snackBar.open("Xóa sách thành công", "Đóng", {
            duration: 3000,
          });
          this.loadBooks();
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error) {
            if (typeof error.error === "string") {
              message = error.error;
            } else if (error.error.message) {
              message = error.error.message;
            } else if (error.error.title) {
              message = error.error.title;
            }
          } else if (error.message) {
            message = error.message;
          }

          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
        },
      });
    }
  }

  deleteSelectedBooks(): void {
    const selectedBooks = this.selection.selected;
    const bookTitles = selectedBooks.map(book => book.title).join(', ');
    
    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedBooks.length} sách đã chọn?\n\n${bookTitles}`)) {
      this.isDeleting = true;
      const bookIds = selectedBooks.map(book => book.id);
      
      this.bookService.deleteMultipleBooks(bookIds).subscribe({
        next: (result) => {
          this.snackBar.open(result.message, "Đóng", {
            duration: 5000,
          });
          
          if (result.failedReasons && result.failedReasons.length > 0) {
            console.warn('Một số sách không thể xóa:', result.failedReasons);
          }
          
          this.loadBooks();
          this.isDeleting = false;
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error?.message) {
            message = error.error.message;
          }
          
          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isDeleting = false;
        },
      });
    }
  }

  deleteAllBooks(): void {
    if (this.books.length === 0) {
      this.snackBar.open("Không có sách nào để xóa", "Đóng", {
        duration: 3000,
      });
      return;
    }

    const confirmMessage = `⚠️ CẢNH BÁO: Bạn đang chuẩn bị xóa TẤT CẢ ${this.books.length} sách!\n\nHành động này không thể hoàn tác. Bạn có chắc chắn muốn tiếp tục?`;
    
    if (confirm(confirmMessage)) {
      this.isDeleting = true;
      const allBookIds = this.books.map(book => book.id);
      
      this.bookService.deleteMultipleBooks(allBookIds).subscribe({
        next: (result) => {
          this.snackBar.open(result.message, "Đóng", {
            duration: 5000,
          });
          
          if (result.failedReasons && result.failedReasons.length > 0) {
            console.warn('Một số sách không thể xóa:', result.failedReasons);
          }
          
          this.loadBooks();
          this.isDeleting = false;
        },
        error: (error) => {
          let message = "Lỗi khi xóa sách";
          if (error.error?.message) {
            message = error.error.message;
          }
          
          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isDeleting = false;
        },
      });
    }
  }
}
