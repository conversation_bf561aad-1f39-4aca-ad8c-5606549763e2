<app-page-container>
  <!-- Header Section -->
  <app-page-header
    title="Báo cáo Sách Quá hạn"
    [subtitle]="getSubtitle()"
    icon="warning"
    [actions]="headerActions">
  </app-page-header>

  <!-- Table Container with Search and Data -->
  <app-table-container
    [showSearch]="true"
    searchPlaceholder="Tìm kiếm theo tên sách, thành viên..."
    [searchValue]="searchTerm"
    [filters]="searchFilters"
    [isLoading]="isLoading"
    [isEmpty]="overdueBooks.length === 0"
    loadingMessage="Đang tải báo cáo sách quá hạn..."
    emptyIcon="warning"
    emptyTitle="Không có sách quá hạn"
    emptyMessage="Hiện tại không có sách nào quá hạn trong hệ thống."
    (searchChange)="onSearchChange($event)"
    (filterChange)="onFilterChange($event)">

    <!-- Overdue Books Table -->
    <div class="table-container">
          <mat-form-field appearance="outline">
            <mat-label>Tìm kiếm</mat-label>
            <input matInput (keyup)="applyFilter($event)" [(ngModel)]="filterValue" placeholder="Tìm theo tên sách, tác giả hoặc thành viên">
            <button *ngIf="filterValue" matSuffix mat-icon-button aria-label="Clear" (click)="clearFilter()">
              <mat-icon>close</mat-icon>
            </button>
            <mat-icon matPrefix>search</mat-icon>
          </mat-form-field>
        </div>

        <div class="summary-info">
          <p>Tổng số sách đang quá hạn: <strong>{{ filteredData.length }}</strong></p>
          <p>Tổng phí phạt: <strong>{{ getTotalFine() | currency:'VND':'symbol':'1.0-0' }}</strong></p>
        </div>

        <table mat-table [dataSource]="filteredData" matSort class="mat-elevation-z2">
          <!-- STT Column -->
          <ng-container matColumnDef="stt">
            <th mat-header-cell *matHeaderCellDef> STT </th>
            <td mat-cell *matCellDef="let record; let i = index"> {{ i + 1 }} </td>
          </ng-container>

          <!-- Book Title Column -->
          <ng-container matColumnDef="bookTitle">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Sách </th>
            <td mat-cell *matCellDef="let record">
              <div class="book-info">
                <div class="book-title">{{ record.bookTitle }}</div>
                <div class="book-author">{{ record.bookAuthor }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Member Name Column -->
          <ng-container matColumnDef="memberName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Thành viên </th>
            <td mat-cell *matCellDef="let record"> {{ record.memberName }} </td>
          </ng-container>

          <!-- Borrow Date Column -->
          <ng-container matColumnDef="borrowDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Ngày mượn </th>
            <td mat-cell *matCellDef="let record"> {{ record.borrowDate | date:'dd/MM/yyyy' }} </td>
          </ng-container>

          <!-- Due Date Column -->
          <ng-container matColumnDef="dueDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Ngày hẹn trả </th>
            <td mat-cell *matCellDef="let record"> {{ record.dueDate | date:'dd/MM/yyyy' }} </td>
          </ng-container>

          <!-- Days Overdue Column -->
          <ng-container matColumnDef="daysOverdue">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Số ngày quá hạn </th>
            <td mat-cell *matCellDef="let record" class="overdue-days"> {{ record.daysOverdue }} </td>
          </ng-container>

          <!-- Fine Column -->
          <ng-container matColumnDef="fine">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Phí phạt </th>
            <td mat-cell *matCellDef="let record"> {{ calculateFine(record) | currency:'VND':'symbol':'1.0-0' }} </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Thao tác </th>
            <td mat-cell *matCellDef="let record">
              <button mat-icon-button color="primary" [routerLink]="['/borrows/details', record.borrowId]" matTooltip="Xem chi tiết">
                <mat-icon>visibility</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

          <!-- Row shown when there is no matching data. -->
          <tr class="mat-row" *matNoDataRow>
            <td class="mat-cell" colspan="8">
              <div class="no-data-message">
                <mat-icon>search_off</mat-icon>
                <p>Không tìm thấy dữ liệu phù hợp với "{{ filterValue }}"</p>
              </div>
            </td>
          </tr>
        </table>

        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>
      </div>
  </app-table-container>
</app-page-container>