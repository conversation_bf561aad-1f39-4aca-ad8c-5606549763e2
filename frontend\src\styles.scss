/* You can add global styles to this file, and also import other style files */

// Import enhanced design system
@import 'styles/variables';
@import 'styles/global';

// Import design tokens and theme variables
:root {
  // Default light theme colors (will be overridden by ThemeService)
  --color-primary: #1976d2;
  --color-primary-variant: #1565c0;
  --color-secondary: #dc004e;
  --color-secondary-variant: #c51162;
  --color-background: #fafafa;
  --color-surface: #ffffff;
  --color-surface-variant: #f5f5f5;
  --color-error: #d32f2f;
  --color-warning: #f57c00;
  --color-success: #388e3c;
  --color-info: #1976d2;
  --color-on-primary: #ffffff;
  --color-on-secondary: #ffffff;
  --color-on-background: #212121;
  --color-on-surface: #212121;
  --color-on-error: #ffffff;
  --color-outline: #e0e0e0;
  --color-shadow: rgba(0, 0, 0, 0.1);

  // Spacing
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  // Border radius
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --radius-extra-large: 24px;

  // Typography
  --font-family: "Roboto", "Helvetica Neue", Arial, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  // Shadows
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --shadow-xl: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);

  // Transitions
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  background-color: var(--color-background);
  color: var(--color-on-background);
  transition: background-color var(--transition-normal), color var(--transition-normal);

  // Ẩn scrollbar toàn cục
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

body {
  margin: 0;
  font-family: var(--font-family);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Ẩn scrollbar cho tất cả elements
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

// Utility Classes
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);

  &.container-sm {
    max-width: 800px;
  }

  &.container-lg {
    max-width: 1400px;
  }

  &.container-fluid {
    max-width: none;
    width: 100%;
  }
}

.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.spacer {
  flex: 1 1 auto;
}

// Flexbox utilities
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

// Spacing utilities
.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

// Material Design Component Overrides
.mat-mdc-card {
  margin: var(--spacing-md);
  border-radius: var(--radius-large) !important;
  background-color: var(--color-surface) !important;
  color: var(--color-on-surface) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all var(--transition-normal);
  border: 1px solid var(--color-outline);

  &:hover {
    box-shadow: var(--shadow-lg) !important;
    transform: translateY(-2px);
  }

  .mat-mdc-card-header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);

    .mat-mdc-card-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-on-surface);
      margin: 0;
    }

    .mat-mdc-card-subtitle {
      color: var(--color-on-surface);
      opacity: 0.7;
      margin: var(--spacing-xs) 0 0 0;
    }
  }

  .mat-mdc-card-content {
    padding: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
  }

  .mat-mdc-card-actions {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
    gap: var(--spacing-sm);
  }
}

.mat-mdc-form-field {
  margin: var(--spacing-sm) 0;
  width: 100%;

  &.mat-form-field-appearance-outline {
    .mat-mdc-form-field-outline {
      border-radius: var(--radius-medium) !important;
    }

    .mat-mdc-form-field-outline-thick {
      color: var(--color-primary) !important;
    }
  }

  .mat-mdc-form-field-label {
    color: var(--color-on-surface) !important;
  }

  .mat-mdc-input-element {
    color: var(--color-on-surface) !important;
  }
}

.mat-mdc-table {
  box-shadow: var(--shadow-md) !important;
  border-radius: var(--radius-large) !important;
  background-color: var(--color-surface) !important;
  overflow: hidden;

  .mat-mdc-header-row {
    background-color: var(--color-surface-variant) !important;

    .mat-mdc-header-cell {
      color: var(--color-on-surface) !important;
      font-weight: var(--font-weight-semibold);
      border-bottom: 1px solid var(--color-outline);
    }
  }

  .mat-mdc-row {
    &:hover {
      background-color: var(--color-surface-variant) !important;
    }

    .mat-mdc-cell {
      color: var(--color-on-surface) !important;
      border-bottom: 1px solid var(--color-outline);
    }
  }
}

.success {
  color: #4caf50;
}

.warning {
  color: #ff9800;
}

.error {
  color: #f44336;
}

/* Snackbar styles */
.snackbar-success {
  background-color: #4caf50 !important;
  color: white !important;
}

.snackbar-error {
  background-color: #f44336 !important;
  color: white !important;
}

.snackbar-info {
  background-color: #2196f3 !important;
  color: white !important;
}

/* Form enhancements */
.mat-mdc-form-field.mat-form-field-appearance-outline {
  .mat-mdc-form-field-outline {
    border-radius: 12px;
  }
}

.mat-mdc-card {
  border-radius: 16px;
}

// Button Overrides
.mat-mdc-raised-button, .mat-mdc-unelevated-button {
  border-radius: var(--radius-medium) !important;
  font-weight: var(--font-weight-semibold) !important;
  text-transform: none !important;
  letter-spacing: 0.5px !important;
  padding: var(--spacing-sm) var(--spacing-lg) !important;
  transition: all var(--transition-fast) !important;
  box-shadow: var(--shadow-sm) !important;

  &:hover {
    box-shadow: var(--shadow-md) !important;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.mat-mdc-outlined-button, .mat-mdc-stroked-button {
  border-radius: var(--radius-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  text-transform: none !important;
  border-width: 2px !important;
  padding: var(--spacing-sm) var(--spacing-lg) !important;
  transition: all var(--transition-fast) !important;

  &:hover {
    background-color: var(--color-primary) !important;
    color: var(--color-on-primary) !important;
    transform: translateY(-1px);
  }
}

.mat-mdc-icon-button {
  border-radius: var(--radius-medium) !important;
  transition: all var(--transition-fast) !important;

  &:hover {
    background-color: var(--color-surface-variant) !important;
    transform: scale(1.1);
  }
}

.mat-mdc-fab {
  box-shadow: var(--shadow-lg) !important;
  border-radius: var(--radius-large) !important;
  transition: all var(--transition-normal) !important;

  &:hover {
    box-shadow: var(--shadow-xl) !important;
    transform: scale(1.05);
  }

  &.mat-mdc-mini-fab {
    border-radius: var(--radius-medium) !important;
  }
}

// Theme-specific styles
.theme-light {
  .glass {
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-lg);
  }

  .loading-overlay {
    background: rgba(255, 255, 255, 0.8);
  }

  // Header user section in light mode
  .user-section .user-button {
    .user-avatar mat-icon {
      color: #000000 !important;
    }

    .user-info {
      .user-name {
        color: #000000 !important;
      }

      .user-role {
        color: #000000 !important;
        background-color: rgba(0, 0, 0, 0.1) !important;
      }
    }

    .dropdown-icon {
      color: #000000 !important;
    }
  }

  // Menu items in light mode
  .mat-mdc-menu-panel {
    background-color: var(--color-surface) !important;

    .mat-mdc-menu-item {
      color: var(--color-on-surface) !important;

      .mat-icon {
        color: var(--color-on-surface) !important;
      }
    }
  }
}

.theme-dark {
  .glass {
    background: rgba(30, 30, 30, 0.95);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
  }

  .loading-overlay {
    background: rgba(18, 18, 18, 0.8);
  }

  // Header user section in dark mode
  .user-section .user-button {
    .user-avatar mat-icon {
      color: #ffffff !important;
    }

    .user-info {
      .user-name {
        color: #ffffff !important;
      }

      .user-role {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.2) !important;
      }
    }

    .dropdown-icon {
      color: #ffffff !important;
    }
  }

  // All buttons should be dark with white text
  .mat-mdc-raised-button, .mat-mdc-unelevated-button {
    background-color: #333333 !important;
    color: #ffffff !important;

    &:hover {
      background-color: #444444 !important;
    }
  }

  .mat-mdc-outlined-button, .mat-mdc-stroked-button {
    border-color: #ffffff !important;
    color: #ffffff !important;

    &:hover {
      background-color: #333333 !important;
      color: #ffffff !important;
    }
  }

  .mat-mdc-icon-button {
    color: #ffffff !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
  }

  // Menu items in dark mode
  .mat-mdc-menu-panel {
    background-color: var(--color-surface) !important;

    .mat-mdc-menu-item {
      color: var(--color-on-surface) !important;

      .mat-icon {
        color: var(--color-on-surface) !important;
      }

      &:hover {
        background-color: var(--color-surface-variant) !important;
      }
    }
  }
}

// Optimized animations with will-change
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translate3d(30px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in-up {
  animation: fadeInUp var(--transition-normal) ease-out;
  will-change: opacity, transform;
}

.slide-in-right {
  animation: slideInRight var(--transition-normal) ease-out;
  will-change: opacity, transform;
}

.scale-in {
  animation: scaleIn var(--transition-fast) ease-out;
  will-change: opacity, transform;
}

// Loading states
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-outline);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Global Material Menu Overrides
.mat-mdc-menu-panel {
  background-color: var(--color-surface) !important;
  border: 1px solid var(--color-outline) !important;
  border-radius: var(--radius-medium) !important;
  box-shadow: var(--shadow-lg) !important;

  .mat-mdc-menu-content {
    padding: var(--spacing-xs) 0 !important;
  }

  .mat-mdc-menu-item {
    color: var(--color-on-surface) !important;
    font-size: var(--font-size-sm) !important;
    line-height: 1.5 !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    min-height: 40px !important;

    .mat-icon {
      color: var(--color-on-surface) !important;
      margin-right: var(--spacing-sm) !important;
    }

    &:hover {
      background-color: var(--color-surface-variant) !important;
    }

    &:focus {
      background-color: var(--color-surface-variant) !important;
    }
  }

  .mat-divider {
    border-top-color: var(--color-outline) !important;
    margin: var(--spacing-xs) 0 !important;
  }
}