import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatBadgeModule } from '@angular/material/badge';
import { RouterModule } from '@angular/router';

export interface QuickAction {
  id: string;
  label: string;
  icon: string;
  color?: 'primary' | 'accent' | 'warn';
  tooltip?: string;
  routerLink?: string;
  click?: () => void;
  disabled?: boolean;
  badge?: string | number;
  shortcut?: string;
  group?: string;
}

@Component({
  selector: 'app-quick-actions',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatTooltipModule,
    MatDividerModule,
    MatBadgeModule,
    RouterModule
  ],
  template: `
    <div class="quick-actions-container">
      <!-- Primary Actions (Always Visible) -->
      <div class="primary-actions">
        <ng-container *ngFor="let action of primaryActions">
          <button
            *ngIf="!action.routerLink"
            mat-fab
            [color]="action.color || 'primary'"
            [disabled]="action.disabled"
            [matTooltip]="getTooltipText(action)"
            (click)="executeAction(action)"
            class="quick-action-button primary-action">
            <mat-icon [matBadge]="action.badge" [matBadgeHidden]="!action.badge">
              {{ action.icon }}
            </mat-icon>
          </button>

          <a
            *ngIf="action.routerLink"
            mat-fab
            [color]="action.color || 'primary'"
            [routerLink]="action.routerLink"
            [matTooltip]="getTooltipText(action)"
            class="quick-action-button primary-action">
            <mat-icon [matBadge]="action.badge" [matBadgeHidden]="!action.badge">
              {{ action.icon }}
            </mat-icon>
          </a>
        </ng-container>
      </div>

      <!-- Secondary Actions (Menu) -->
      <div class="secondary-actions" *ngIf="secondaryActions.length > 0">
        <button
          mat-fab
          color="accent"
          [matMenuTriggerFor]="actionsMenu"
          matTooltip="Thêm hành động"
          class="quick-action-button menu-trigger">
          <mat-icon>add</mat-icon>
        </button>

        <mat-menu #actionsMenu="matMenu" class="quick-actions-menu">
          <ng-container *ngFor="let group of actionGroups">
            <div class="menu-group" *ngIf="group.actions.length > 0">
              <div class="menu-group-title" *ngIf="group.name">{{ group.name }}</div>
              
              <ng-container *ngFor="let action of group.actions">
                <button
                  *ngIf="!action.routerLink"
                  mat-menu-item
                  [disabled]="action.disabled"
                  (click)="executeAction(action)"
                  class="quick-action-menu-item">
                  <mat-icon [matBadge]="action.badge" [matBadgeHidden]="!action.badge">
                    {{ action.icon }}
                  </mat-icon>
                  <span class="action-label">{{ action.label }}</span>
                  <span class="action-shortcut" *ngIf="action.shortcut">{{ action.shortcut }}</span>
                </button>

                <a
                  *ngIf="action.routerLink"
                  mat-menu-item
                  [routerLink]="action.routerLink"
                  class="quick-action-menu-item">
                  <mat-icon [matBadge]="action.badge" [matBadgeHidden]="!action.badge">
                    {{ action.icon }}
                  </mat-icon>
                  <span class="action-label">{{ action.label }}</span>
                  <span class="action-shortcut" *ngIf="action.shortcut">{{ action.shortcut }}</span>
                </a>
              </ng-container>
            </div>
            <mat-divider *ngIf="group.name && !group.isLast"></mat-divider>
          </ng-container>
        </mat-menu>
      </div>

      <!-- Compact Mode Toggle -->
      <div class="compact-toggle" *ngIf="showCompactToggle">
        <button
          mat-mini-fab
          color="primary"
          (click)="toggleCompactMode()"
          [matTooltip]="isCompact ? 'Mở rộng' : 'Thu gọn'"
          class="compact-button">
          <mat-icon>{{ isCompact ? 'expand_more' : 'expand_less' }}</mat-icon>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .quick-actions-container {
      position: fixed;
      bottom: 24px;
      right: 24px;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 16px;
    }

    .primary-actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: center;
    }

    .secondary-actions {
      display: flex;
      align-items: center;
    }

    .quick-action-button {
      box-shadow: var(--shadow-lg);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .quick-action-button:hover {
      transform: scale(1.1);
      box-shadow: var(--shadow-xl);
    }

    .quick-action-button:active {
      transform: scale(0.95);
    }

    .primary-action {
      width: 56px;
      height: 56px;
    }

    .menu-trigger {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, var(--color-accent), var(--color-accent-variant));
    }

    .compact-button {
      width: 40px;
      height: 40px;
      opacity: 0.8;
    }

    .compact-button:hover {
      opacity: 1;
      transform: scale(1.05);
    }

    .quick-actions-menu {
      max-width: 300px;
      min-width: 250px;
    }

    .menu-group {
      padding: 8px 0;
    }

    .menu-group-title {
      padding: 8px 16px 4px;
      font-size: 12px;
      font-weight: 600;
      color: var(--color-primary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .quick-action-menu-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      min-height: 48px;
      position: relative;
    }

    .quick-action-menu-item mat-icon {
      color: var(--color-on-surface);
      opacity: 0.7;
    }

    .action-label {
      flex: 1;
      font-weight: 500;
    }

    .action-shortcut {
      font-size: 11px;
      color: var(--color-on-surface);
      opacity: 0.6;
      background: var(--color-surface-variant);
      padding: 2px 6px;
      border-radius: 4px;
      font-family: monospace;
    }

    /* Compact Mode */
    .quick-actions-container.compact .primary-actions {
      display: none;
    }

    .quick-actions-container.compact .secondary-actions {
      transform: scale(0.8);
    }

    /* Animation States */
    .quick-action-button {
      animation: slideInUp 0.3s ease-out;
    }

    .primary-actions .quick-action-button:nth-child(1) {
      animation-delay: 0.1s;
    }

    .primary-actions .quick-action-button:nth-child(2) {
      animation-delay: 0.2s;
    }

    .primary-actions .quick-action-button:nth-child(3) {
      animation-delay: 0.3s;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    /* Ripple Effect */
    .quick-action-button::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: translate(-50%, -50%);
      transition: width 0.3s, height 0.3s;
    }

    .quick-action-button:active::before {
      width: 100px;
      height: 100px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .quick-actions-container {
        bottom: 16px;
        right: 16px;
        gap: 12px;
      }

      .primary-action {
        width: 48px;
        height: 48px;
      }

      .menu-trigger {
        width: 40px;
        height: 40px;
      }

      .compact-button {
        width: 32px;
        height: 32px;
      }

      .quick-actions-menu {
        max-width: 280px;
        min-width: 220px;
      }
    }

    @media (max-width: 480px) {
      .quick-actions-container {
        bottom: 12px;
        right: 12px;
        gap: 8px;
      }

      .primary-action {
        width: 44px;
        height: 44px;
      }

      .menu-trigger {
        width: 36px;
        height: 36px;
      }

      .quick-action-menu-item {
        padding: 10px 12px;
        min-height: 44px;
      }
    }

    /* Accessibility */
    @media (prefers-reduced-motion: reduce) {
      .quick-action-button {
        animation: none;
        transition: none;
      }

      .quick-action-button:hover {
        transform: none;
      }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
      .quick-action-button {
        border: 2px solid var(--color-on-surface);
      }

      .action-shortcut {
        border: 1px solid var(--color-on-surface);
      }
    }
  `]
})
export class QuickActionsComponent {
  @Input() actions: QuickAction[] = [];
  @Input() maxPrimaryActions: number = 3;
  @Input() showCompactToggle: boolean = true;
  @Input() isCompact: boolean = false;

  @Output() actionExecuted = new EventEmitter<QuickAction>();
  @Output() compactModeChanged = new EventEmitter<boolean>();

  get primaryActions(): QuickAction[] {
    return this.actions.slice(0, this.maxPrimaryActions);
  }

  get secondaryActions(): QuickAction[] {
    return this.actions.slice(this.maxPrimaryActions);
  }

  get actionGroups(): Array<{name: string, actions: QuickAction[], isLast: boolean}> {
    const groups = new Map<string, QuickAction[]>();
    
    this.secondaryActions.forEach(action => {
      const groupName = action.group || 'Khác';
      if (!groups.has(groupName)) {
        groups.set(groupName, []);
      }
      groups.get(groupName)!.push(action);
    });

    const groupArray = Array.from(groups.entries()).map(([name, actions], index, array) => ({
      name,
      actions,
      isLast: index === array.length - 1
    }));

    return groupArray;
  }

  executeAction(action: QuickAction): void {
    if (action.disabled) return;

    if (action.click) {
      action.click();
    }

    this.actionExecuted.emit(action);
  }

  toggleCompactMode(): void {
    this.isCompact = !this.isCompact;
    this.compactModeChanged.emit(this.isCompact);
  }

  getTooltipText(action: QuickAction): string {
    let tooltip = action.tooltip || action.label;
    if (action.shortcut) {
      tooltip += ` (${action.shortcut})`;
    }
    return tooltip;
  }
}
