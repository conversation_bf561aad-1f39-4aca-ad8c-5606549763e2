import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { MatCardModule } from "@angular/material/card";
import { MatTableModule } from "@angular/material/table";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatChipsModule } from "@angular/material/chips";
import { MatSnackBarModule, MatSnackBar } from "@angular/material/snack-bar";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { FormsModule } from "@angular/forms";

import { ShelfService } from "src/app/services/shelf.service";
import { Shelf } from "src/app/models/shelf.model";
import { ZoneService, Zone } from "src/app/services/zone.service";
import { Router, RouterModule } from "@angular/router";
import { PageContainerComponent } from "../../shared/page-container/page-container.component";
import { PageHeaderComponent, PageHeaderAction } from "../../shared/page-header/page-header.component";
import { TableContainerComponent, SearchFilter } from "../../shared/table-container/table-container.component";

@Component({
  selector: "app-shelves-list",
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    PageContainerComponent,
    PageHeaderComponent,
    TableContainerComponent,
  ],
  template: `
    <app-page-container>
      <!-- Header Section -->
      <app-page-header
        title="Quản lý Kệ Sách"
        [subtitle]="'Tổng số: ' + shelves.length + ' kệ'"
        icon="inventory_2"
        [actions]="headerActions">
      </app-page-header>

      <!-- Table Container with Search and Data -->
      <app-table-container
        [showSearch]="true"
        searchPlaceholder="Tìm kiếm kệ sách theo tên hoặc khu vực..."
        [searchValue]="searchTerm"
        [filters]="searchFilters"
        [isLoading]="isLoading"
        [isEmpty]="filteredShelves.length === 0"
        loadingMessage="Đang tải danh sách kệ sách..."
        emptyIcon="inventory_2"
        emptyTitle="Chưa có kệ sách nào"
        emptyMessage="Thư viện chưa có kệ sách nào. Hãy thêm kệ sách đầu tiên để bắt đầu."
        emptyActionText="Thêm kệ mới"
        emptyActionIcon="add"
        (searchChange)="onSearchChange($event)"
        (filterChange)="onFilterChange($event)"
        (emptyAction)="navigateToAdd()">

      <!-- Content Card -->
      <mat-card class="content-card">
        <table
          mat-table
          [dataSource]="filteredShelves"
          class="shelves-table"
        >
          <ng-container matColumnDef="stt">
            <th mat-header-cell *matHeaderCellDef>STT</th>
            <td mat-cell *matCellDef="let s; let i = index">{{ i + 1 }}</td>
          </ng-container>

          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Tên kệ</th>
            <td mat-cell *matCellDef="let s">{{ s.name }}</td>
          </ng-container>

          <ng-container matColumnDef="zone">
            <th mat-header-cell *matHeaderCellDef>Khu vực</th>
            <td mat-cell *matCellDef="let s">{{ getZoneName(s.zoneId) }}</td>
          </ng-container>

          <ng-container matColumnDef="capacity">
            <th mat-header-cell *matHeaderCellDef>Sức chứa</th>
            <td mat-cell *matCellDef="let s">{{ s.capacity }}</td>
          </ng-container>

          <ng-container matColumnDef="currentCount">
            <th mat-header-cell *matHeaderCellDef>Hiện có</th>
            <td mat-cell *matCellDef="let s">
              {{ s.currentCount }} / {{ s.capacity }} sách
            </td>
          </ng-container>

          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Trạng thái</th>
            <td mat-cell *matCellDef="let s">
              <mat-chip [color]="getStatusColor(s.status)" selected>
                {{ getStatusLabel(s.status) }}
              </mat-chip>
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Thao tác</th>
            <td mat-cell *matCellDef="let s">
              <button mat-icon-button color="primary"
                      [routerLink]="['/shelves', s.id]"
                      matTooltip="Xem chi tiết">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button color="accent"
                      [routerLink]="['/shelves/edit', s.id]"
                      matTooltip="Chỉnh sửa">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn"
                      [routerLink]="['/shelves', s.id, 'books']"
                      matTooltip="Quản lý sách">
                <mat-icon>book</mat-icon>
              </button>
              <button mat-icon-button color="warn"
                      (click)="deleteShelf(s)"
                      matTooltip="Xóa">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </app-table-container>
    </app-page-container>
  `,
  styles: [`
    .shelves-container {
      padding: var(--spacing-lg);
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-lg);
      gap: var(--spacing-md);

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
      }
    }

    .header-content {
      flex: 1;

      .page-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--color-on-background);
        margin: 0 0 var(--spacing-xs) 0;

        mat-icon {
          font-size: 32px;
          width: 32px;
          height: 32px;
          color: var(--color-primary);
        }
      }

      .page-subtitle {
        color: var(--color-on-background);
        opacity: 0.7;
        margin: 0;
        font-size: var(--font-size-md);
      }
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
      align-items: center;

      @media (max-width: 768px) {
        justify-content: flex-end;
        margin-top: var(--spacing-md);
      }
    }

    .search-container {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      gap: var(--spacing-sm);
    }

    .search-field {
      flex: 1;
    }

    .content-card {
      border-radius: var(--radius-large);
      box-shadow: var(--shadow-md);
      overflow: hidden;
      padding: var(--spacing-lg);
    }

    .shelves-table {
      width: 100%;

      .mat-mdc-header-cell {
        font-weight: var(--font-weight-semibold);
        color: var(--color-on-surface);
      }

      .mat-mdc-cell {
        color: var(--color-on-surface);
      }
    }
  `],
})
export class ShelvesListComponent implements OnInit {
  shelves: Shelf[] = [];
  zones: Zone[] = [];
  displayedColumns: string[] = [
    "stt",
    "name",
    "zone",
    "capacity",
    "currentCount",
    "status",
    "actions",
  ];
  searchTerm: string = "";
  filteredShelves: Shelf[] = [];
  headerActions: PageHeaderAction[] = [];
  searchFilters: SearchFilter[] = [];
  isLoading = false;

  constructor(
    private shelfService: ShelfService,
    private zoneService: ZoneService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.initializeHeaderActions();
    this.initializeSearchFilters();
  }

  ngOnInit(): void {
    this.loadZones();
    this.loadShelves();
  }

  private initializeHeaderActions(): void {
    this.headerActions = [
      {
        label: 'Quản lý khu vực',
        icon: 'map',
        color: 'accent',
        click: () => this.goToZoneList()
      },
      {
        label: 'Thêm kệ mới',
        icon: 'add',
        color: 'primary',
        click: () => this.addShelf()
      }
    ];
  }

  private initializeSearchFilters(): void {
    this.searchFilters = [
      {
        key: 'zone',
        label: 'Khu vực',
        type: 'select',
        options: this.zones.map(zone => ({
          value: zone.id,
          label: zone.name
        }))
      },
      {
        key: 'status',
        label: 'Trạng thái',
        type: 'select',
        options: [
          { value: 'active', label: 'Hoạt động' },
          { value: 'inactive', label: 'Không hoạt động' },
          { value: 'maintenance', label: 'Bảo trì' }
        ]
      }
    ];
  }

  onSearchChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.onSearch();
  }

  onFilterChange(filters: SearchFilter[]): void {
    // Handle filter changes
    this.onSearch();
  }

  navigateToAdd(): void {
    this.addShelf();
  }

  loadShelves(): void {
    this.isLoading = true;
    this.shelfService.getAllShelves().subscribe({
      next: (data) => {
        this.shelves = data;
        this.filteredShelves = [...this.shelves];
        this.isLoading = false;
      },
      error: (err) => {
        this.snackBar.open("Lỗi khi tải danh sách kệ sách", "Đóng", {
          duration: 3000,
        });
        this.isLoading = false;
      },
    });
  }

  loadZones(): void {
    this.zoneService.getAllZones().subscribe({
      next: (zones) => (this.zones = zones),
      error: (err) => {
        console.error("Lỗi tải khu vực:", err);
        this.snackBar.open('Lỗi khi tải danh sách khu vực', 'Đóng', { duration: 3000 });
      },
    });
  }

  getZoneName(zoneId: number): string {
    const zone = this.zones.find((z) => z.id === zoneId);
    return zone ? zone.name : "Không xác định";
  }

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case "active":
        return "primary";
      case "inactive":
        return "warn";
      case "maintenance":
        return "accent";
      default:
        return "";
    }
  }

  getStatusLabel(status: string): string {
    switch (status?.toLowerCase()) {
      case "active":
        return "Hoạt động";
      case "inactive":
        return "Không hoạt động";
      case "maintenance":
        return "Bảo trì";
      default:
        return status || "Không xác định";
    }
  }

  editShelf(shelf: Shelf): void {
    this.router.navigate(["/shelves/edit", shelf.id]);
  }

  deleteShelf(shelf: Shelf): void {
    if (confirm(`Bạn có chắc chắn muốn xóa kệ "${shelf.name}"?`)) {
      this.shelfService.deleteShelf(shelf.id).subscribe({
        next: () => {
          this.snackBar.open("Xóa kệ thành công", "Đóng", { duration: 3000 });
          this.loadShelves();
        },
        error: (err) => {
          this.snackBar.open(
            "Lỗi khi xóa kệ. Có thể kệ đang chứa sách.",
            "Đóng",
            { duration: 3000 }
          );
        },
      });
    }
  }

  goToZoneList(): void {
    this.router.navigate(["/zones"]);
  }

  addShelf(): void {
    this.router.navigate(["/shelves/add"]);
  }

  onSearch(): void {
    this.applySearch();
  }

  clearSearch(): void {
    this.searchTerm = "";
    this.applySearch();
  }

  applySearch(): void {
    if (!this.searchTerm.trim()) {
      this.filteredShelves = [...this.shelves];
      return;
    }
    
    this.filteredShelves = this.shelves.filter(shelf => {
      const matchesName = shelf.name.toLowerCase().includes(this.searchTerm.toLowerCase());
      const matchesZone = this.getZoneName(shelf.zoneId).toLowerCase().includes(this.searchTerm.toLowerCase());
      return matchesName || matchesZone;
    });
  }
}
