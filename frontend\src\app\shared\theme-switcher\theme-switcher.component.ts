import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDividerModule } from '@angular/material/divider';
import { Subject, takeUntil } from 'rxjs';

export interface Theme {
  id: string;
  name: string;
  icon: string;
  primary: string;
  accent: string;
  isDark: boolean;
}

@Component({
  selector: 'app-theme-switcher',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatTooltipModule,
    MatSlideToggleModule,
    MatDividerModule
  ],
  template: `
    <button 
      mat-icon-button 
      [matMenuTriggerFor]="themeMenu"
      matTooltip="Chủ đề giao diện"
      class="theme-trigger">
      <mat-icon>{{ currentTheme.icon }}</mat-icon>
    </button>

    <mat-menu #themeMenu="matMenu" class="theme-menu">
      <div class="theme-menu-header" (click)="$event.stopPropagation()">
        <h3>Chủ đề giao diện</h3>
        <div class="dark-mode-toggle">
          <mat-slide-toggle
            [checked]="isDarkMode"
            (change)="toggleDarkMode($event.checked)"
            color="primary">
            Chế độ tối
          </mat-slide-toggle>
        </div>
      </div>

      <mat-divider></mat-divider>

      <div class="theme-options" (click)="$event.stopPropagation()">
        <div class="theme-section">
          <h4>Chủ đề sáng</h4>
          <div class="theme-grid">
            <button
              *ngFor="let theme of lightThemes"
              mat-button
              class="theme-option"
              [class.active]="currentTheme.id === theme.id"
              (click)="selectTheme(theme)">
              <div class="theme-preview">
                <div class="color-primary" [style.background-color]="theme.primary"></div>
                <div class="color-accent" [style.background-color]="theme.accent"></div>
              </div>
              <span class="theme-name">{{ theme.name }}</span>
            </button>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="theme-section">
          <h4>Chủ đề tối</h4>
          <div class="theme-grid">
            <button
              *ngFor="let theme of darkThemes"
              mat-button
              class="theme-option"
              [class.active]="currentTheme.id === theme.id"
              (click)="selectTheme(theme)">
              <div class="theme-preview">
                <div class="color-primary" [style.background-color]="theme.primary"></div>
                <div class="color-accent" [style.background-color]="theme.accent"></div>
              </div>
              <span class="theme-name">{{ theme.name }}</span>
            </button>
          </div>
        </div>
      </div>

      <mat-divider></mat-divider>

      <div class="theme-actions" (click)="$event.stopPropagation()">
        <button mat-menu-item (click)="resetToDefault()">
          <mat-icon>refresh</mat-icon>
          <span>Đặt lại mặc định</span>
        </button>
        <button mat-menu-item (click)="exportTheme()">
          <mat-icon>download</mat-icon>
          <span>Xuất cài đặt</span>
        </button>
        <button mat-menu-item (click)="importTheme()">
          <mat-icon>upload</mat-icon>
          <span>Nhập cài đặt</span>
        </button>
      </div>
    </mat-menu>

    <!-- Hidden file input for import -->
    <input
      #fileInput
      type="file"
      accept=".json"
      style="display: none"
      (change)="onFileSelected($event)">
  `,
  styles: [`
    .theme-trigger {
      transition: all 0.3s ease;
    }

    .theme-trigger:hover {
      transform: rotate(180deg);
    }

    .theme-menu {
      width: 320px;
      max-width: 90vw;
    }

    .theme-menu-header {
      padding: 16px 20px;
      background: var(--color-surface-variant);
    }

    .theme-menu-header h3 {
      margin: 0 0 12px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--color-on-surface);
    }

    .dark-mode-toggle {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .theme-options {
      padding: 16px 20px;
      max-height: 400px;
      overflow-y: auto;
    }

    .theme-section {
      margin-bottom: 20px;
    }

    .theme-section:last-child {
      margin-bottom: 0;
    }

    .theme-section h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--color-on-surface);
      opacity: 0.8;
    }

    .theme-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }

    .theme-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 12px 8px;
      border-radius: 8px;
      border: 2px solid transparent;
      transition: all 0.2s ease;
      min-height: 80px;
      text-transform: none;
    }

    .theme-option:hover {
      background: var(--color-surface-variant);
      border-color: var(--color-outline);
    }

    .theme-option.active {
      border-color: var(--color-primary);
      background: rgba(25, 118, 210, 0.1);
    }

    .theme-preview {
      display: flex;
      gap: 4px;
      align-items: center;
    }

    .color-primary,
    .color-accent {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 2px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .theme-name {
      font-size: 12px;
      font-weight: 500;
      color: var(--color-on-surface);
      text-align: center;
      line-height: 1.2;
    }

    .theme-actions {
      padding: 8px 0;
      background: var(--color-surface-variant);
    }

    .theme-actions button {
      width: 100%;
      justify-content: flex-start;
    }

    /* Custom Scrollbar */
    .theme-options::-webkit-scrollbar {
      width: 6px;
    }

    .theme-options::-webkit-scrollbar-track {
      background: var(--color-surface-variant);
    }

    .theme-options::-webkit-scrollbar-thumb {
      background: var(--color-outline);
      border-radius: 3px;
    }

    .theme-options::-webkit-scrollbar-thumb:hover {
      background: var(--color-on-surface);
      opacity: 0.5;
    }

    /* Responsive Design */
    @media (max-width: 480px) {
      .theme-menu {
        width: 100vw;
        max-width: none;
      }

      .theme-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;
      }

      .theme-option {
        padding: 8px 4px;
        min-height: 60px;
      }

      .color-primary,
      .color-accent {
        width: 16px;
        height: 16px;
      }

      .theme-name {
        font-size: 10px;
      }
    }

    /* Animation */
    .theme-option {
      animation: fadeIn 0.2s ease-out;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: scale(0.9);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }

    /* Theme transition */
    :host {
      transition: all 0.3s ease;
    }
  `]
})
export class ThemeSwitcherComponent implements OnInit, OnDestroy {
  currentTheme: Theme = this.getDefaultTheme();
  isDarkMode: boolean = false;
  
  private destroy$ = new Subject<void>();

  lightThemes: Theme[] = [
    {
      id: 'light-blue',
      name: 'Xanh dương',
      icon: 'wb_sunny',
      primary: '#1976d2',
      accent: '#ff4081',
      isDark: false
    },
    {
      id: 'light-green',
      name: 'Xanh lá',
      icon: 'wb_sunny',
      primary: '#388e3c',
      accent: '#ff9800',
      isDark: false
    },
    {
      id: 'light-purple',
      name: 'Tím',
      icon: 'wb_sunny',
      primary: '#7b1fa2',
      accent: '#4caf50',
      isDark: false
    },
    {
      id: 'light-orange',
      name: 'Cam',
      icon: 'wb_sunny',
      primary: '#f57c00',
      accent: '#e91e63',
      isDark: false
    }
  ];

  darkThemes: Theme[] = [
    {
      id: 'dark-blue',
      name: 'Xanh dương tối',
      icon: 'brightness_2',
      primary: '#90caf9',
      accent: '#f48fb1',
      isDark: true
    },
    {
      id: 'dark-green',
      name: 'Xanh lá tối',
      icon: 'brightness_2',
      primary: '#a5d6a7',
      accent: '#ffcc02',
      isDark: true
    },
    {
      id: 'dark-purple',
      name: 'Tím tối',
      icon: 'brightness_2',
      primary: '#ce93d8',
      accent: '#81c784',
      isDark: true
    },
    {
      id: 'dark-orange',
      name: 'Cam tối',
      icon: 'brightness_2',
      primary: '#ffb74d',
      accent: '#f06292',
      isDark: true
    }
  ];

  ngOnInit(): void {
    this.loadSavedTheme();
    this.detectSystemTheme();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getDefaultTheme(): Theme {
    return this.lightThemes[0];
  }

  private loadSavedTheme(): void {
    const savedThemeId = localStorage.getItem('selected-theme');
    const savedDarkMode = localStorage.getItem('dark-mode') === 'true';
    
    if (savedThemeId) {
      const allThemes = [...this.lightThemes, ...this.darkThemes];
      const savedTheme = allThemes.find(t => t.id === savedThemeId);
      if (savedTheme) {
        this.currentTheme = savedTheme;
      }
    }
    
    this.isDarkMode = savedDarkMode;
    this.applyTheme();
  }

  private detectSystemTheme(): void {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      if (!localStorage.getItem('dark-mode')) {
        this.isDarkMode = true;
        this.selectTheme(this.darkThemes[0]);
      }
    }

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('user-theme-preference')) {
        this.isDarkMode = e.matches;
        this.selectTheme(e.matches ? this.darkThemes[0] : this.lightThemes[0]);
      }
    });
  }

  selectTheme(theme: Theme): void {
    this.currentTheme = theme;
    this.isDarkMode = theme.isDark;
    this.applyTheme();
    this.saveTheme();
  }

  toggleDarkMode(isDark: boolean): void {
    this.isDarkMode = isDark;
    const targetThemes = isDark ? this.darkThemes : this.lightThemes;
    
    // Try to find equivalent theme in the other mode
    const currentBaseName = this.currentTheme.id.replace(/^(light|dark)-/, '');
    const equivalentTheme = targetThemes.find(t => t.id.includes(currentBaseName));
    
    this.selectTheme(equivalentTheme || targetThemes[0]);
  }

  private applyTheme(): void {
    const root = document.documentElement;
    
    // Apply CSS custom properties
    root.style.setProperty('--color-primary', this.currentTheme.primary);
    root.style.setProperty('--color-accent', this.currentTheme.accent);
    
    // Apply dark/light mode class
    document.body.classList.toggle('dark-theme', this.isDarkMode);
    document.body.classList.toggle('light-theme', !this.isDarkMode);
    
    // Update theme-color meta tag
    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
    if (themeColorMeta) {
      themeColorMeta.setAttribute('content', this.currentTheme.primary);
    }
  }

  private saveTheme(): void {
    localStorage.setItem('selected-theme', this.currentTheme.id);
    localStorage.setItem('dark-mode', this.isDarkMode.toString());
    localStorage.setItem('user-theme-preference', 'true');
  }

  resetToDefault(): void {
    this.selectTheme(this.getDefaultTheme());
    localStorage.removeItem('user-theme-preference');
  }

  exportTheme(): void {
    const themeConfig = {
      themeId: this.currentTheme.id,
      isDarkMode: this.isDarkMode,
      customColors: {
        primary: this.currentTheme.primary,
        accent: this.currentTheme.accent
      },
      exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(themeConfig, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `library-theme-${this.currentTheme.id}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }

  importTheme(): void {
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    fileInput?.click();
  }

  onFileSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const themeConfig = JSON.parse(e.target?.result as string);
        
        if (themeConfig.themeId) {
          const allThemes = [...this.lightThemes, ...this.darkThemes];
          const importedTheme = allThemes.find(t => t.id === themeConfig.themeId);
          
          if (importedTheme) {
            this.selectTheme(importedTheme);
          }
        }
      } catch (error) {
        console.error('Error importing theme:', error);
        // Show error message to user
      }
    };
    
    reader.readAsText(file);
  }
}
