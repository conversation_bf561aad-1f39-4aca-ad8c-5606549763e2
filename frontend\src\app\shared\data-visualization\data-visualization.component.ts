import { Component, Input, OnInit, OnChanges, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

Chart.register(...registerables);

export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    tension?: number;
  }>;
}

export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: any;
  scales?: any;
  animation?: any;
}

@Component({
  selector: 'app-data-visualization',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatTooltipModule
  ],
  template: `
    <mat-card class="chart-card">
      <mat-card-header class="chart-header">
        <div class="header-content">
          <div class="header-text">
            <mat-card-title>{{ title }}</mat-card-title>
            <mat-card-subtitle *ngIf="subtitle">{{ subtitle }}</mat-card-subtitle>
          </div>
          <div class="header-actions">
            <button 
              mat-icon-button 
              [matMenuTriggerFor]="chartMenu"
              matTooltip="Tùy chọn biểu đồ">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #chartMenu="matMenu">
              <button mat-menu-item (click)="downloadChart()">
                <mat-icon>download</mat-icon>
                <span>Tải xuống</span>
              </button>
              <button mat-menu-item (click)="refreshData()">
                <mat-icon>refresh</mat-icon>
                <span>Làm mới</span>
              </button>
              <button mat-menu-item (click)="toggleFullscreen()">
                <mat-icon>{{ isFullscreen ? 'fullscreen_exit' : 'fullscreen' }}</mat-icon>
                <span>{{ isFullscreen ? 'Thu nhỏ' : 'Toàn màn hình' }}</span>
              </button>
            </mat-menu>
          </div>
        </div>
      </mat-card-header>

      <mat-card-content class="chart-content" [class.fullscreen]="isFullscreen">
        <div class="chart-container" [class.loading]="isLoading">
          <canvas 
            #chartCanvas
            [width]="canvasWidth"
            [height]="canvasHeight">
          </canvas>
          
          <!-- Loading Overlay -->
          <div *ngIf="isLoading" class="loading-overlay">
            <div class="loading-spinner"></div>
            <p>Đang tải dữ liệu...</p>
          </div>

          <!-- No Data State -->
          <div *ngIf="!isLoading && !hasData" class="no-data-state">
            <mat-icon>bar_chart</mat-icon>
            <h3>Không có dữ liệu</h3>
            <p>{{ noDataMessage || 'Chưa có dữ liệu để hiển thị biểu đồ' }}</p>
            <button mat-raised-button color="primary" (click)="refreshData()">
              <mat-icon>refresh</mat-icon>
              Làm mới
            </button>
          </div>
        </div>

        <!-- Chart Legend (Custom) -->
        <div *ngIf="showCustomLegend && hasData" class="custom-legend">
          <div 
            *ngFor="let item of legendItems" 
            class="legend-item"
            (click)="toggleDataset(item.index)"
            [class.disabled]="item.hidden">
            <div 
              class="legend-color" 
              [style.background-color]="item.color">
            </div>
            <span class="legend-label">{{ item.label }}</span>
            <span class="legend-value">{{ item.value }}</span>
          </div>
        </div>

        <!-- Chart Statistics -->
        <div *ngIf="showStats && hasData" class="chart-stats">
          <div class="stat-item">
            <span class="stat-label">Tổng:</span>
            <span class="stat-value">{{ totalValue | number }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Trung bình:</span>
            <span class="stat-value">{{ averageValue | number:'1.1-1' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Cao nhất:</span>
            <span class="stat-value">{{ maxValue | number }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Thấp nhất:</span>
            <span class="stat-value">{{ minValue | number }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .chart-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      border-radius: 16px;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--color-outline);
      overflow: hidden;
    }

    .chart-header {
      background: var(--color-surface-variant);
      border-bottom: 1px solid var(--color-outline);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    .header-text {
      flex: 1;
    }

    .chart-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 24px;
      position: relative;
    }

    .chart-content.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1000;
      background: var(--color-surface);
      padding: 32px;
    }

    .chart-container {
      flex: 1;
      position: relative;
      min-height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .chart-container canvas {
      max-width: 100%;
      max-height: 100%;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      z-index: 10;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--color-outline);
      border-top: 4px solid var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .no-data-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      color: var(--color-on-surface);
      opacity: 0.7;
      text-align: center;
      padding: 40px;
    }

    .no-data-state mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      opacity: 0.5;
    }

    .custom-legend {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--color-outline);
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.2s ease;
    }

    .legend-item:hover {
      background: var(--color-surface-variant);
    }

    .legend-item.disabled {
      opacity: 0.5;
    }

    .legend-color {
      width: 16px;
      height: 16px;
      border-radius: 4px;
      flex-shrink: 0;
    }

    .legend-label {
      font-weight: 500;
      color: var(--color-on-surface);
    }

    .legend-value {
      font-weight: 600;
      color: var(--color-primary);
      margin-left: auto;
    }

    .chart-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--color-outline);
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px;
      background: var(--color-surface-variant);
      border-radius: 8px;
    }

    .stat-label {
      font-size: 12px;
      color: var(--color-on-surface);
      opacity: 0.7;
      margin-bottom: 4px;
    }

    .stat-value {
      font-size: 18px;
      font-weight: 600;
      color: var(--color-primary);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .chart-content {
        padding: 16px;
      }

      .chart-container {
        min-height: 250px;
      }

      .custom-legend {
        flex-direction: column;
        gap: 8px;
      }

      .chart-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
      }
    }

    @media (max-width: 480px) {
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .chart-stats {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class DataVisualizationComponent implements OnInit, OnChanges {
  @Input() title: string = '';
  @Input() subtitle?: string;
  @Input() chartType: ChartType = 'bar';
  @Input() chartData: ChartData | null = null;
  @Input() chartOptions: ChartOptions = {};
  @Input() isLoading: boolean = false;
  @Input() noDataMessage?: string;
  @Input() showCustomLegend: boolean = false;
  @Input() showStats: boolean = false;
  @Input() canvasWidth: number = 400;
  @Input() canvasHeight: number = 300;

  @ViewChild('chartCanvas', { static: true }) chartCanvas!: ElementRef<HTMLCanvasElement>;

  chart: Chart | null = null;
  isFullscreen: boolean = false;
  legendItems: Array<{index: number, label: string, color: string, value: string, hidden: boolean}> = [];

  get hasData(): boolean {
    return !!(this.chartData && this.chartData.datasets.length > 0);
  }

  get totalValue(): number {
    if (!this.chartData) return 0;
    return this.chartData.datasets.reduce((total, dataset) => {
      return total + dataset.data.reduce((sum, value) => sum + value, 0);
    }, 0);
  }

  get averageValue(): number {
    if (!this.chartData) return 0;
    const total = this.totalValue;
    const count = this.chartData.datasets.reduce((count, dataset) => count + dataset.data.length, 0);
    return count > 0 ? total / count : 0;
  }

  get maxValue(): number {
    if (!this.chartData) return 0;
    return Math.max(...this.chartData.datasets.flatMap(dataset => dataset.data));
  }

  get minValue(): number {
    if (!this.chartData) return 0;
    return Math.min(...this.chartData.datasets.flatMap(dataset => dataset.data));
  }

  ngOnInit(): void {
    this.initializeChart();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['chartData'] || changes['chartType'] || changes['chartOptions']) {
      this.updateChart();
    }
  }

  private initializeChart(): void {
    if (!this.hasData) return;

    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    const config: ChartConfiguration = {
      type: this.chartType,
      data: this.chartData!,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: !this.showCustomLegend
          }
        },
        ...this.chartOptions
      }
    };

    this.chart = new Chart(ctx, config);
    this.updateLegendItems();
  }

  private updateChart(): void {
    if (!this.chart) {
      this.initializeChart();
      return;
    }

    if (this.hasData) {
      this.chart.data = this.chartData!;
      this.chart.update();
      this.updateLegendItems();
    }
  }

  private updateLegendItems(): void {
    if (!this.showCustomLegend || !this.chartData) return;

    this.legendItems = this.chartData.datasets.map((dataset, index) => ({
      index,
      label: dataset.label,
      color: Array.isArray(dataset.backgroundColor) 
        ? dataset.backgroundColor[0] 
        : dataset.backgroundColor || '#000',
      value: dataset.data.reduce((sum, value) => sum + value, 0).toString(),
      hidden: false
    }));
  }

  toggleDataset(index: number): void {
    if (!this.chart) return;

    const meta = this.chart.getDatasetMeta(index);
    meta.hidden = !meta.hidden;
    this.legendItems[index].hidden = meta.hidden;
    this.chart.update();
  }

  downloadChart(): void {
    if (!this.chart) return;

    const link = document.createElement('a');
    link.download = `${this.title || 'chart'}.png`;
    link.href = this.chart.toBase64Image();
    link.click();
  }

  refreshData(): void {
    // Emit event to parent component to refresh data
    // This would be implemented based on specific needs
  }

  toggleFullscreen(): void {
    this.isFullscreen = !this.isFullscreen;
    
    setTimeout(() => {
      if (this.chart) {
        this.chart.resize();
      }
    }, 100);
  }
}
