// ===================================
// GLOBAL STYLES
// ===================================

@import 'variables';

// Reset and Base Styles
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--color-on-background);
  background-color: var(--color-background);
  transition: background-color var(--transition-base), color var(--transition-base);
}

// Typography Improvements
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-on-surface);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin: 0 0 var(--spacing-4) 0;
  color: var(--color-on-surface);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
  
  &:hover {
    color: var(--color-primary-variant);
    text-decoration: underline;
  }
  
  &:focus-visible {
    @extend .focus-visible;
  }
}

// Enhanced Material Design Components
.mat-mdc-card {
  border-radius: var(--card-border-radius) !important;
  box-shadow: var(--shadow-md) !important;
  border: 1px solid var(--color-outline) !important;
  background: var(--color-surface) !important;
  
  .mat-mdc-card-content {
    padding: var(--card-padding) !important;
  }
}

.mat-mdc-button, .mat-mdc-raised-button, .mat-mdc-outlined-button {
  border-radius: var(--button-border-radius) !important;
  font-weight: var(--font-weight-medium) !important;
  text-transform: none !important;
  transition: all var(--transition-fast) !important;
  
  &:not([disabled]):hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg) !important;
  }
}

.mat-mdc-fab {
  box-shadow: var(--shadow-lg) !important;
  transition: all var(--transition-base) !important;
  
  &:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-xl) !important;
  }
}

.mat-mdc-form-field {
  .mat-mdc-form-field-outline {
    border-radius: var(--form-field-border-radius) !important;
  }
  
  .mat-mdc-form-field-label {
    color: var(--color-on-surface-variant) !important;
  }
  
  &.mat-focused .mat-mdc-form-field-label {
    color: var(--color-primary) !important;
  }
}

.mat-mdc-table {
  background: var(--color-surface) !important;
  border-radius: var(--radius-xl) !important;
  overflow: hidden;
  box-shadow: var(--shadow-sm) !important;
  
  .mat-mdc-header-row {
    background: var(--color-surface-variant) !important;
    
    .mat-mdc-header-cell {
      color: var(--color-on-surface) !important;
      font-weight: var(--font-weight-semibold) !important;
      border-bottom: 1px solid var(--color-outline) !important;
    }
  }
  
  .mat-mdc-row {
    transition: background-color var(--transition-fast) !important;
    
    &:hover {
      background: var(--color-surface-variant) !important;
    }
    
    .mat-mdc-cell {
      border-bottom: 1px solid var(--color-outline) !important;
      color: var(--color-on-surface) !important;
    }
  }
}

.mat-mdc-snack-bar-container {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  
  &.success-snackbar {
    background: var(--color-success) !important;
    color: var(--color-on-success) !important;
  }
  
  &.error-snackbar {
    background: var(--color-error) !important;
    color: var(--color-on-error) !important;
  }
  
  &.warning-snackbar {
    background: var(--color-warning) !important;
    color: var(--color-on-warning) !important;
  }
  
  &.info-snackbar {
    background: var(--color-info) !important;
    color: var(--color-on-info) !important;
  }
}

// Menu and Dialog Enhancements
.mat-mdc-menu-panel {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--color-outline) !important;
  background: var(--color-surface) !important;
  
  .mat-mdc-menu-item {
    transition: background-color var(--transition-fast) !important;
    
    &:hover {
      background: var(--color-surface-variant) !important;
    }
  }
}

.mat-mdc-dialog-container {
  border-radius: var(--radius-2xl) !important;
  box-shadow: var(--shadow-2xl) !important;
  border: 1px solid var(--color-outline) !important;
  background: var(--color-surface) !important;
}

// Chip Enhancements
.mat-mdc-chip {
  border-radius: var(--radius-full) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: all var(--transition-fast) !important;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md) !important;
  }
}

// Progress Indicators
.mat-mdc-progress-bar {
  border-radius: var(--radius-full) !important;
  overflow: hidden;
}

.mat-mdc-progress-spinner {
  circle {
    stroke: var(--color-primary) !important;
  }
}

// Expansion Panel
.mat-expansion-panel {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-sm) !important;
  border: 1px solid var(--color-outline) !important;
  background: var(--color-surface) !important;
  
  &:not(.mat-expanded) {
    .mat-expansion-panel-header:hover {
      background: var(--color-surface-variant) !important;
    }
  }
}

// Slider
.mat-mdc-slider {
  .mdc-slider__track {
    border-radius: var(--radius-full) !important;
  }
  
  .mdc-slider__thumb {
    box-shadow: var(--shadow-md) !important;
  }
}

// Tabs
.mat-mdc-tab-group {
  .mat-mdc-tab-header {
    border-bottom: 1px solid var(--color-outline) !important;
  }
  
  .mat-mdc-tab {
    transition: all var(--transition-fast) !important;
    
    &:hover {
      background: var(--color-surface-variant) !important;
    }
  }
}

// Utility Classes
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-bg {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-variant));
}

.text-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hover-lift {
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Responsive Utilities
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
  
  .mat-mdc-card .mat-mdc-card-content {
    padding: var(--card-padding-mobile) !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}

// Dark Theme Specific Adjustments
.dark-theme {
  .mat-mdc-card {
    background: var(--color-surface) !important;
    border-color: var(--color-outline) !important;
  }
  
  .glass-effect {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
