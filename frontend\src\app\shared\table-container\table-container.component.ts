import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';
import { LoadingStateComponent } from '../loading-state/loading-state.component';
import { EmptyStateComponent } from '../empty-state/empty-state.component';

export interface SearchFilter {
  key: string;
  label: string;
  type: 'text' | 'select';
  options?: { value: any; label: string }[];
  placeholder?: string;
  value?: any;
}

@Component({
  selector: 'app-table-container',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    FormsModule,
    LoadingStateComponent,
    EmptyStateComponent
  ],
  template: `
    <div class="table-container">
      <mat-card class="table-card">
        <!-- Search and Filters -->
        <div class="search-section" *ngIf="showSearch || filters.length > 0">
          <div class="search-filters">
            <!-- Main Search -->
            <mat-form-field 
              *ngIf="showSearch" 
              appearance="outline" 
              class="search-field">
              <mat-label>{{ searchPlaceholder }}</mat-label>
              <input 
                matInput 
                [(ngModel)]="searchValue"
                (input)="onSearchChange()"
                [placeholder]="searchPlaceholder">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <!-- Additional Filters -->
            <ng-container *ngFor="let filter of filters">
              <mat-form-field 
                *ngIf="filter.type === 'text'" 
                appearance="outline" 
                class="filter-field">
                <mat-label>{{ filter.label }}</mat-label>
                <input 
                  matInput 
                  [(ngModel)]="filter.value"
                  (input)="onFilterChange()"
                  [placeholder]="filter.placeholder || ''">
              </mat-form-field>

              <mat-form-field 
                *ngIf="filter.type === 'select'" 
                appearance="outline" 
                class="filter-field">
                <mat-label>{{ filter.label }}</mat-label>
                <mat-select 
                  [(ngModel)]="filter.value"
                  (selectionChange)="onFilterChange()">
                  <mat-option value="">Tất cả</mat-option>
                  <mat-option 
                    *ngFor="let option of filter.options" 
                    [value]="option.value">
                    {{ option.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </ng-container>

            <!-- Clear Filters Button -->
            <button 
              *ngIf="hasActiveFilters()"
              mat-stroked-button 
              (click)="clearFilters()"
              class="clear-filters-btn">
              <mat-icon>clear</mat-icon>
              Xóa bộ lọc
            </button>
          </div>
        </div>

        <!-- Table Content -->
        <div class="table-content">
          <!-- Loading State -->
          <app-loading-state 
            *ngIf="isLoading"
            [message]="loadingMessage"
            [size]="50">
          </app-loading-state>

          <!-- Empty State -->
          <app-empty-state
            *ngIf="!isLoading && isEmpty"
            [icon]="emptyIcon"
            [title]="emptyTitle"
            [message]="emptyMessage"
            [actionText]="emptyActionText"
            [actionIcon]="emptyActionIcon"
            (action)="onEmptyAction()">
          </app-empty-state>

          <!-- Table Content Slot -->
          <div *ngIf="!isLoading && !isEmpty" class="table-wrapper">
            <ng-content></ng-content>
          </div>
        </div>
      </mat-card>
    </div>
  `,
  styles: [`
    .table-container {
      width: 100%;
    }

    .table-card {
      border-radius: 16px;
      box-shadow: var(--shadow-md);
      border: 1px solid var(--color-outline);
      overflow: hidden;
    }

    .search-section {
      padding: 24px 32px 0;
      border-bottom: 1px solid var(--color-outline);
      margin-bottom: 0;
    }

    .search-filters {
      display: flex;
      gap: 16px;
      align-items: flex-end;
      flex-wrap: wrap;
      margin-bottom: 24px;
    }

    .search-field {
      flex: 2;
      min-width: 300px;
    }

    .filter-field {
      flex: 1;
      min-width: 150px;
      max-width: 200px;
    }

    .clear-filters-btn {
      border-radius: 8px;
      height: 40px;
      color: var(--color-warning);
      border-color: var(--color-warning);
    }

    .clear-filters-btn:hover {
      background-color: rgba(245, 124, 0, 0.1);
    }

    .table-content {
      min-height: 200px;
    }

    .table-wrapper {
      padding: 0;
    }

    .table-wrapper ::ng-deep .mat-mdc-table {
      width: 100%;
      background: transparent;
    }

    .table-wrapper ::ng-deep .mat-mdc-header-row {
      background: var(--color-surface-variant);
    }

    .table-wrapper ::ng-deep .mat-mdc-row:hover {
      background: rgba(0, 0, 0, 0.04);
    }

    .table-wrapper ::ng-deep .mat-mdc-cell,
    .table-wrapper ::ng-deep .mat-mdc-header-cell {
      padding: 16px;
      border-bottom: 1px solid var(--color-outline);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .search-section {
        padding: 20px 24px 0;
      }

      .search-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
      }

      .search-field,
      .filter-field {
        flex: none;
        min-width: auto;
        max-width: none;
        width: 100%;
      }

      .clear-filters-btn {
        align-self: flex-start;
      }

      .table-wrapper {
        overflow-x: auto;
      }
    }

    @media (max-width: 480px) {
      .search-section {
        padding: 16px 20px 0;
      }

      .search-filters {
        margin-bottom: 16px;
      }

      .table-wrapper ::ng-deep .mat-mdc-cell,
      .table-wrapper ::ng-deep .mat-mdc-header-cell {
        padding: 12px 8px;
        font-size: 14px;
      }
    }

    /* Animation */
    .table-card {
      animation: slideInUp 0.3s ease-out;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `]
})
export class TableContainerComponent {
  @Input() showSearch: boolean = true;
  @Input() searchPlaceholder: string = 'Tìm kiếm...';
  @Input() searchValue: string = '';
  @Input() filters: SearchFilter[] = [];
  @Input() isLoading: boolean = false;
  @Input() isEmpty: boolean = false;
  @Input() loadingMessage: string = 'Đang tải dữ liệu...';
  @Input() emptyIcon: string = 'inbox';
  @Input() emptyTitle: string = 'Không có dữ liệu';
  @Input() emptyMessage: string = 'Không tìm thấy dữ liệu nào.';
  @Input() emptyActionText?: string;
  @Input() emptyActionIcon?: string;

  @Output() searchChange = new EventEmitter<string>();
  @Output() filterChange = new EventEmitter<SearchFilter[]>();
  @Output() emptyAction = new EventEmitter<void>();

  onSearchChange(): void {
    this.searchChange.emit(this.searchValue);
  }

  onFilterChange(): void {
    this.filterChange.emit(this.filters);
  }

  clearFilters(): void {
    this.searchValue = '';
    this.filters.forEach(filter => filter.value = '');
    this.onSearchChange();
    this.onFilterChange();
  }

  hasActiveFilters(): boolean {
    return this.searchValue.trim() !== '' || 
           this.filters.some(filter => filter.value && filter.value !== '');
  }

  onEmptyAction(): void {
    this.emptyAction.emit();
  }
}
