import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule, Sort } from '@angular/material/sort';

import { CategoryService } from '../../services/category.service';
import { Category, CategorySearchParams } from '../../models/category.model';

@Component({
  selector: 'app-category-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatCardModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatSortModule
  ],
  template: `
    <div class="category-list-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>category</mat-icon>
            Quản lý Thể loại
          </mat-card-title>
          <mat-card-subtitle>
            Quản lý các thể loại sách trong thư viện
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <!-- Search and Actions -->
          <div class="actions-bar">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Tìm kiếm thể loại</mat-label>
              <input matInput 
                     [(ngModel)]="searchQuery" 
                     (input)="searchCategories()"
                     placeholder="Nhập tên hoặc mô tả thể loại...">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
            
            <button mat-raised-button color="primary" 
                    [routerLink]="['/categories/add']">
              <mat-icon>add</mat-icon>
              Thêm thể loại
            </button>
          </div>

          <!-- Loading State -->
          <div *ngIf="isLoading" class="loading-container">
            <mat-spinner diameter="50"></mat-spinner>
            <p>Đang tải danh sách thể loại...</p>
          </div>

          <!-- Categories Table -->
          <div *ngIf="!isLoading" class="table-container">
            <table mat-table [dataSource]="categories" matSort (matSortChange)="onSortChange($event)">
              <!-- STT Column -->
              <ng-container matColumnDef="stt">
                <th mat-header-cell *matHeaderCellDef>STT</th>
                <td mat-cell *matCellDef="let category; let i = index">{{ i + 1 }}</td>
              </ng-container>
              
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef mat-sort-header="name">Tên thể loại</th>
                <td mat-cell *matCellDef="let category">
                  <div class="category-name">
                    <strong>{{ category.name }}</strong>
                    <small *ngIf="category.description">{{ category.description }}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Book Count Column -->
              <ng-container matColumnDef="bookCount">
                <th mat-header-cell *matHeaderCellDef mat-sort-header="bookCount">Số sách</th>
                <td mat-cell *matCellDef="let category">
                  <span class="book-count-chip">{{ category.bookCount }} cuốn</span>
                </td>
              </ng-container>

              <!-- Created Date Column -->
              <ng-container matColumnDef="createdAt">
                <th mat-header-cell *matHeaderCellDef mat-sort-header="createdAt">Ngày tạo</th>
                <td mat-cell *matCellDef="let category">
                  {{ formatDate(category.createdAt) }}
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Thao tác</th>
                <td mat-cell *matCellDef="let category">
                  <button mat-icon-button color="primary" 
                          [routerLink]="['/categories', category.id]"
                          matTooltip="Xem chi tiết">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button color="accent" 
                          [routerLink]="['/categories/edit', category.id]"
                          matTooltip="Chỉnh sửa">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" 
                          (click)="deleteCategory(category)" 
                          [disabled]="category.bookCount > 0"
                          [matTooltip]="category.bookCount > 0 ? 'Không thể xóa thể loại có sách' : 'Xóa thể loại'">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <!-- Empty State -->
            <div *ngIf="categories.length === 0" class="empty-state">
              <mat-icon class="empty-icon">category</mat-icon>
              <h3>Chưa có thể loại nào</h3>
              <p>Hãy thêm thể loại đầu tiên để bắt đầu phân loại sách</p>
              <button mat-raised-button color="primary" [routerLink]="['/categories/add']">
                <mat-icon>add</mat-icon>
                Thêm thể loại đầu tiên
              </button>
            </div>
          </div>

          <!-- Pagination -->
          <mat-paginator *ngIf="!isLoading && totalCount > 0"
                         [length]="totalCount"
                         [pageSize]="pageSize"
                         [pageSizeOptions]="[5, 10, 25, 50]"
                         [pageIndex]="currentPage - 1"
                         (page)="onPageChange($event)"
                         showFirstLastButtons>
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .category-list-container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 0 16px;
    }

    .actions-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      gap: 16px;
    }

    .search-field {
      flex: 1;
      max-width: 400px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      gap: 20px;
    }

    .table-container {
      margin-top: 20px;
    }

    .category-name {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .category-name small {
      color: rgba(0, 0, 0, 0.6);
      font-size: 12px;
    }

    .book-count-chip {
      display: inline-block;
      padding: 4px 8px;
      background-color: #e3f2fd;
      color: #1976d2;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: rgba(0, 0, 0, 0.6);
    }

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-state h3 {
      margin: 16px 0 8px 0;
      color: rgba(0, 0, 0, 0.8);
    }

    .empty-state p {
      margin-bottom: 24px;
    }

    @media (max-width: 768px) {
      .actions-bar {
        flex-direction: column;
        align-items: stretch;
      }

      .search-field {
        max-width: none;
      }
    }
  `]
})
export class CategoryListComponent implements OnInit {
  categories: Category[] = [];
  displayedColumns = ['stt', 'name', 'bookCount', 'createdAt', 'actions'];
  
  isLoading = false;
  searchQuery = '';
  
  // Pagination
  totalCount = 0;
  currentPage = 1;
  pageSize = 10;
  
  // Sorting
  sortBy = 'name';
  sortDirection: 'asc' | 'desc' = 'asc';

  constructor(
    private categoryService: CategoryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadCategories();
  }

  loadCategories(): void {
    this.isLoading = true;
    
    const searchParams: CategorySearchParams = {
      query: this.searchQuery || undefined,
      page: this.currentPage,
      pageSize: this.pageSize,
      sortBy: this.sortBy,
      sortDirection: this.sortDirection
    };

    this.categoryService.searchCategories(searchParams).subscribe({
      next: (result) => {
        this.categories = result.categories;
        this.totalCount = result.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open('Lỗi khi tải danh sách thể loại: ' + error.message, 'Đóng', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
        this.isLoading = false;
      }
    });
  }

  searchCategories(): void {
    this.currentPage = 1;
    this.loadCategories();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadCategories();
  }

  onSortChange(sort: Sort): void {
    this.sortBy = sort.active;
    this.sortDirection = sort.direction as 'asc' | 'desc' || 'asc';
    this.currentPage = 1;
    this.loadCategories();
  }

  deleteCategory(category: Category): void {
    if (category.bookCount > 0) {
      this.snackBar.open('Không thể xóa thể loại có chứa sách', 'Đóng', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa thể loại "${category.name}"?`)) {
      this.categoryService.deleteCategory(category.id).subscribe({
        next: () => {
          this.snackBar.open('Xóa thể loại thành công!', 'Đóng', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.loadCategories();
        },
        error: (error) => {
          this.snackBar.open('Lỗi khi xóa thể loại: ' + error.message, 'Đóng', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('vi-VN');
  }
}
