// Base shelf interface
export interface BaseShelf {
  name: string;
  description?: string;
  zoneId: number;
  capacity: number;
  status: ShelfStatus;
}

// Full shelf with all properties (for display)
export interface Shelf extends BaseShelf {
  id: number;
  zoneName: string;
  currentCount: number;
  availableSpace: number;
  createdAt: Date;
  updatedAt?: Date;
}

// For creating new shelf
export interface CreateShelf extends BaseShelf {
  // Inherits all BaseShelf properties
}

// For updating existing shelf
export interface UpdateShelf extends BaseShelf {
  id: number;
}

// Shelf status enum
export enum ShelfStatus {
  ACTIVE = 'Active',
  INACTIVE = 'Inactive',
  MAINTENANCE = 'Maintenance',
  FULL = 'Full'
}

// Book assignment interfaces
export interface AssignBookToShelf {
  shelfId: number;
  bookId: number;
  locationCode?: string;
  quantity: number;
}

export interface RemoveBookFromShelf {
  shelfId: number;
  bookId: number;
  quantity?: number; // If not specified, remove all
}

// Book in shelf interface
export interface BookInShelf {
  bookId: number;
  title: string;
  author: string;
  isbn?: string;
  categoryName: string;
  locationCode?: string;
  stockQuantity: number; // Actually contains OnShelfQuantity from backend
  totalQuantity: number;
  shelfLocation: string; // "Zone A - Shelf 1 - A1"
}

// Available locations response
export interface ShelfLocations {
  availableLocations: string[];
  usedLocations: UsedLocation[];
  capacity: number;
  currentCount: number;
}

export interface UsedLocation {
  locationCode: string;
  bookTitle: string;
  bookAuthor: string;
}

// Shelf validation result
export interface ShelfValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
