import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Router, ActivatedRoute } from "@angular/router";
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from "@angular/forms";
import { MatCardModule } from "@angular/material/card";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatButtonModule } from "@angular/material/button";
import { MatSelectModule } from "@angular/material/select";
import { MatIconModule } from "@angular/material/icon";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { MatProgressBarModule } from "@angular/material/progress-bar";
import {
  Member,
  CreateMember,
  UpdateMember,
  MEMBER_STATUS_OPTIONS,
  MemberStatus,
} from "../../models/member.model";
import { MemberService } from "../../services/member.service";
import { PageContainerComponent } from "../../shared/page-container/page-container.component";
import { FormContainerComponent, FormAction } from "../../shared/form-container/form-container.component";

@Component({
  selector: "app-member-form",
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressBarModule,
    PageContainerComponent,
    FormContainerComponent,
  ],
  template: `
    <app-page-container>
      <app-form-container
        [title]="isEditMode ? 'Chỉnh sửa thành viên' : 'Thêm thành viên mới'"
        [subtitle]="isEditMode ? 'Cập nhật thông tin thành viên' : 'Thêm thành viên mới vào thư viện'"
        [icon]="isEditMode ? 'edit' : 'person_add'"
        [isLoading]="isLoading"
        [showCloseButton]="true"
        [actions]="formActions"
        (close)="onCancel()">

        <form [formGroup]="memberForm" (ngSubmit)="onSubmit()" class="member-form">
          <!-- Personal Information Card -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Thông tin cá nhân</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Tên <span class="required">*</span></mat-label>
                  <input
                    matInput
                    formControlName="firstName"
                    placeholder="Nhập tên..."
                  />
                  <mat-error
                    *ngIf="memberForm.get('firstName')?.hasError('required')"
                  >
                    Tên là bắt buộc
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Họ <span class="required">*</span></mat-label>
                  <input
                    matInput
                    formControlName="lastName"
                    placeholder="Nhập họ..."
                  />
                  <mat-error
                    *ngIf="memberForm.get('lastName')?.hasError('required')"
                  >
                    Họ là bắt buộc
                  </mat-error>
                </mat-form-field>
              </div>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Email <span class="required">*</span></mat-label>
                  <input
                    matInput
                    type="email"
                    formControlName="email"
                    placeholder="Nhập email..."
                  />
                  <mat-error
                    *ngIf="memberForm.get('email')?.hasError('required')"
                  >
                    Email là bắt buộc
                  </mat-error>
                  <mat-error *ngIf="memberForm.get('email')?.hasError('email')">
                    Email không đúng định dạng
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Số điện thoại</mat-label>
                  <input
                    matInput
                    formControlName="phone"
                    placeholder="Nhập số điện thoại..."
                  />
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Địa chỉ</mat-label>
                <textarea
                  matInput
                  formControlName="address"
                  rows="3"
                  placeholder="Nhập địa chỉ..."
                ></textarea>
              </mat-form-field>
            </mat-card-content>
          </mat-card>

          <!-- Status Information Card (Only in edit mode) -->
          <mat-card class="form-section" *ngIf="isEditMode">
            <mat-card-header>
              <mat-card-title>Trạng thái thành viên</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label
                    >Trạng thái <span class="required">*</span></mat-label
                  >
                  <mat-select formControlName="status">
                    <mat-option
                      *ngFor="let status of statusOptions"
                      [value]="status.value"
                    >
                      {{ status.label }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Notes Card -->
          <mat-card class="form-section">
            <mat-card-header>
              <mat-card-title>Ghi chú</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Ghi chú</mat-label>
                <textarea
                  matInput
                  formControlName="notes"
                  rows="4"
                  placeholder="Nhập ghi chú về thành viên (tùy chọn)..."
                ></textarea>
              </mat-form-field>
            </mat-card-content>
          </mat-card>
        </form>
      </app-form-container>
    </app-page-container>
  `,
  styles: [
    `
      .member-form {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }

      .form-section {
        margin-bottom: 24px;
      }

      .form-row {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
      }

      .form-row mat-form-field {
        flex: 1;
        min-width: 200px;
      }

      .full-width {
        width: 100%;
      }

      .required {
        color: #f44336;
      }

      mat-card-header {
        margin-bottom: 16px;
      }

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      mat-card-actions {
        padding: 16px 24px;
        gap: 8px;
      }

      mat-progress-bar {
        margin-bottom: 16px;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .form-row {
          flex-direction: column;
        }

        .form-row mat-form-field {
          min-width: 100%;
        }
      }
    `,
  ],
})
export class MemberFormComponent implements OnInit {
  memberForm: FormGroup;
  isEditMode = false;
  isLoading = false;
  isSubmitting = false;
  memberId?: number;
  statusOptions = MEMBER_STATUS_OPTIONS;
  formActions: FormAction[] = [];

  constructor(
    private fb: FormBuilder,
    private memberService: MemberService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.memberForm = this.createForm();
    this.initializeFormActions();
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params["id"]) {
        this.isEditMode = true;
        this.memberId = +params["id"];
        this.loadMember();
      }
    });
  }

  private initializeFormActions(): void {
    this.formActions = [
      {
        label: 'Hủy',
        type: 'button',
        icon: 'arrow_back',
        click: () => this.onCancel()
      },
      {
        label: this.isEditMode ? 'Cập nhật' : 'Thêm thành viên',
        type: 'submit',
        color: 'primary',
        icon: this.isEditMode ? 'save' : 'add',
        disabled: this.memberForm?.invalid || this.isSubmitting
      }
    ];
  }

  onCancel(): void {
    this.goBack();
  }

  createForm(): FormGroup {
    return this.fb.group({
      firstName: ["", [Validators.required]],
      lastName: ["", [Validators.required]],
      email: ["", [Validators.required, Validators.email]],
      phone: [""],
      address: [""],
      status: [MemberStatus.Active],
      notes: [""],
    });
  }

  loadMember(): void {
    if (!this.memberId) return;

    this.isLoading = true;
    this.memberService.getMember(this.memberId).subscribe({
      next: (member) => {
        this.memberForm.patchValue({
          firstName: member.firstName,
          lastName: member.lastName,
          email: member.email,
          phone: member.phone,
          address: member.address,
          status: member.status,
          notes: member.notes,
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open("Lỗi khi tải thông tin thành viên", "Đóng", {
          duration: 3000,
        });
        this.isLoading = false;
        this.goBack();
      },
    });
  }

  onSubmit(): void {
    if (this.memberForm.invalid || this.isSubmitting) {
      return;
    }

    this.isSubmitting = true;
    const formValue = this.memberForm.value;

    if (this.isEditMode && this.memberId) {
      const updateData: UpdateMember = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        phone: formValue.phone,
        address: formValue.address,
        status: formValue.status,
        notes: formValue.notes,
      };

      this.memberService.updateMember(this.memberId, updateData).subscribe({
        next: () => {
          this.snackBar.open("Cập nhật thành viên thành công!", "Đóng", {
            duration: 3000,
          });
          this.router.navigate(["/members"]);
        },
        error: (error) => {
          console.log("❌ Error response:", error);
          const message =
            typeof error.error === "string"
              ? error.error
              : error.error?.message ||
                error.message ||
                "Lỗi khi cập nhật thành viên";

          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isSubmitting = false;
        },
      });
    } else {
      const createData: CreateMember = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        phone: formValue.phone,
        address: formValue.address,
        notes: formValue.notes,
      };

      this.memberService.createMember(createData).subscribe({
        next: () => {
          this.snackBar.open("Thêm thành viên thành công!", "Đóng", {
            duration: 3000,
          });
          this.router.navigate(["/members"]);
        },
        error: (error) => {
          console.log("❌ Error response:", error);
          const message =
            typeof error.error === "string"
              ? error.error
              : error.error?.message ||
                error.message ||
                "Lỗi khi thêm thành viên";

          this.snackBar.open(message, "Đóng", {
            duration: 5000,
          });
          this.isSubmitting = false;
        },
      });
    }
  }

  goBack(): void {
    this.router.navigate(["/members"]);
  }
}
