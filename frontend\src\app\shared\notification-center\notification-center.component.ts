import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { Subject, takeUntil } from 'rxjs';

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionText?: string;
  actionCallback?: () => void;
  autoHide?: boolean;
  duration?: number;
}

@Component({
  selector: 'app-notification-center',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatBadgeModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule,
    MatCardModule
  ],
  template: `
    <button 
      mat-icon-button 
      [matMenuTriggerFor]="notificationMenu"
      matTooltip="Thông báo"
      class="notification-trigger">
      <mat-icon 
        [matBadge]="unreadCount" 
        [matBadgeHidden]="unreadCount === 0"
        matBadgeColor="warn"
        matBadgeSize="small">
        notifications
      </mat-icon>
    </button>

    <mat-menu #notificationMenu="matMenu" class="notification-menu" xPosition="before">
      <div class="notification-header" (click)="$event.stopPropagation()">
        <h3>Thông báo</h3>
        <div class="header-actions">
          <button 
            mat-icon-button 
            matTooltip="Đánh dấu tất cả đã đọc"
            (click)="markAllAsRead()"
            [disabled]="unreadCount === 0">
            <mat-icon>done_all</mat-icon>
          </button>
          <button 
            mat-icon-button 
            matTooltip="Xóa tất cả"
            (click)="clearAll()"
            [disabled]="notifications.length === 0">
            <mat-icon>clear_all</mat-icon>
          </button>
        </div>
      </div>

      <mat-divider></mat-divider>

      <div class="notification-list" (click)="$event.stopPropagation()">
        <div 
          *ngFor="let notification of notifications; trackBy: trackByNotificationId" 
          class="notification-item"
          [class.unread]="!notification.read"
          [class]="'notification-' + notification.type"
          (click)="markAsRead(notification)">
          
          <div class="notification-icon">
            <mat-icon>{{ getNotificationIcon(notification.type) }}</mat-icon>
          </div>

          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ getRelativeTime(notification.timestamp) }}</div>
          </div>

          <div class="notification-actions">
            <button 
              *ngIf="notification.actionText && notification.actionCallback"
              mat-button 
              color="primary"
              (click)="executeAction(notification, $event)">
              {{ notification.actionText }}
            </button>
            <button 
              mat-icon-button 
              matTooltip="Xóa"
              (click)="removeNotification(notification.id, $event)">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>

        <div *ngIf="notifications.length === 0" class="no-notifications">
          <mat-icon>notifications_none</mat-icon>
          <p>Không có thông báo nào</p>
        </div>
      </div>

      <mat-divider *ngIf="notifications.length > 0"></mat-divider>

      <div *ngIf="notifications.length > 0" class="notification-footer" (click)="$event.stopPropagation()">
        <button mat-button (click)="viewAllNotifications()">
          Xem tất cả thông báo
        </button>
      </div>
    </mat-menu>
  `,
  styles: [`
    .notification-trigger {
      position: relative;
    }

    .notification-menu {
      width: 400px;
      max-width: 90vw;
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background: var(--color-surface-variant);
    }

    .notification-header h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--color-on-surface);
    }

    .header-actions {
      display: flex;
      gap: 4px;
    }

    .notification-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .notification-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px 20px;
      border-left: 4px solid transparent;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
    }

    .notification-item:hover {
      background: var(--color-surface-variant);
    }

    .notification-item.unread {
      background: rgba(25, 118, 210, 0.05);
    }

    .notification-item.unread::before {
      content: '';
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--color-primary);
    }

    .notification-item.notification-info {
      border-left-color: var(--color-info);
    }

    .notification-item.notification-success {
      border-left-color: var(--color-success);
    }

    .notification-item.notification-warning {
      border-left-color: var(--color-warning);
    }

    .notification-item.notification-error {
      border-left-color: var(--color-error);
    }

    .notification-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 4px;
    }

    .notification-info .notification-icon {
      background: rgba(25, 118, 210, 0.1);
      color: var(--color-info);
    }

    .notification-success .notification-icon {
      background: rgba(56, 142, 60, 0.1);
      color: var(--color-success);
    }

    .notification-warning .notification-icon {
      background: rgba(245, 124, 0, 0.1);
      color: var(--color-warning);
    }

    .notification-error .notification-icon {
      background: rgba(211, 47, 47, 0.1);
      color: var(--color-error);
    }

    .notification-content {
      flex: 1;
      min-width: 0;
    }

    .notification-title {
      font-weight: 600;
      font-size: 14px;
      color: var(--color-on-surface);
      margin-bottom: 4px;
      line-height: 1.3;
    }

    .notification-message {
      font-size: 13px;
      color: var(--color-on-surface);
      opacity: 0.8;
      line-height: 1.4;
      margin-bottom: 6px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .notification-time {
      font-size: 11px;
      color: var(--color-on-surface);
      opacity: 0.6;
    }

    .notification-actions {
      display: flex;
      flex-direction: column;
      gap: 4px;
      align-items: flex-end;
    }

    .no-notifications {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: var(--color-on-surface);
      opacity: 0.6;
      text-align: center;
    }

    .no-notifications mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 12px;
    }

    .notification-footer {
      padding: 12px 20px;
      text-align: center;
      background: var(--color-surface-variant);
    }

    /* Responsive Design */
    @media (max-width: 480px) {
      .notification-menu {
        width: 100vw;
        max-width: none;
      }

      .notification-item {
        padding: 12px 16px;
        gap: 8px;
      }

      .notification-icon {
        width: 32px;
        height: 32px;
      }

      .notification-actions {
        flex-direction: row;
        gap: 2px;
      }
    }

    /* Scrollbar Styling */
    .notification-list::-webkit-scrollbar {
      width: 6px;
    }

    .notification-list::-webkit-scrollbar-track {
      background: var(--color-surface-variant);
    }

    .notification-list::-webkit-scrollbar-thumb {
      background: var(--color-outline);
      border-radius: 3px;
    }

    .notification-list::-webkit-scrollbar-thumb:hover {
      background: var(--color-on-surface);
      opacity: 0.5;
    }
  `]
})
export class NotificationCenterComponent implements OnInit, OnDestroy {
  notifications: Notification[] = [];
  private destroy$ = new Subject<void>();

  get unreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  ngOnInit(): void {
    this.loadNotifications();
    this.setupAutoHideNotifications();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadNotifications(): void {
    // Load notifications from service or local storage
    // This would be implemented based on your notification service
    this.notifications = this.getMockNotifications();
  }

  private setupAutoHideNotifications(): void {
    // Setup timer to auto-hide notifications
    setInterval(() => {
      const now = new Date();
      this.notifications = this.notifications.filter(notification => {
        if (notification.autoHide && notification.duration) {
          const elapsed = now.getTime() - notification.timestamp.getTime();
          return elapsed < notification.duration;
        }
        return true;
      });
    }, 1000);
  }

  private getMockNotifications(): Notification[] {
    return [
      {
        id: '1',
        type: 'success',
        title: 'Thêm sách thành công',
        message: 'Cuốn sách "Lập trình Angular" đã được thêm vào thư viện',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        read: false
      },
      {
        id: '2',
        type: 'warning',
        title: 'Sách sắp hết hạn',
        message: '5 cuốn sách sẽ hết hạn mượn trong 2 ngày tới',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        read: false,
        actionText: 'Xem chi tiết',
        actionCallback: () => console.log('View overdue books')
      },
      {
        id: '3',
        type: 'info',
        title: 'Thành viên mới',
        message: 'Nguyễn Văn A đã đăng ký thành viên mới',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        read: true
      }
    ];
  }

  trackByNotificationId(index: number, notification: Notification): string {
    return notification.id;
  }

  getNotificationIcon(type: string): string {
    const icons = {
      info: 'info',
      success: 'check_circle',
      warning: 'warning',
      error: 'error'
    };
    return icons[type as keyof typeof icons] || 'notifications';
  }

  getRelativeTime(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Vừa xong';
    if (minutes < 60) return `${minutes} phút trước`;
    if (hours < 24) return `${hours} giờ trước`;
    return `${days} ngày trước`;
  }

  markAsRead(notification: Notification): void {
    notification.read = true;
    // Update in service/storage
  }

  markAllAsRead(): void {
    this.notifications.forEach(n => n.read = true);
    // Update in service/storage
  }

  removeNotification(id: string, event: Event): void {
    event.stopPropagation();
    this.notifications = this.notifications.filter(n => n.id !== id);
    // Update in service/storage
  }

  clearAll(): void {
    this.notifications = [];
    // Update in service/storage
  }

  executeAction(notification: Notification, event: Event): void {
    event.stopPropagation();
    if (notification.actionCallback) {
      notification.actionCallback();
    }
    this.markAsRead(notification);
  }

  viewAllNotifications(): void {
    // Navigate to full notifications page
    console.log('Navigate to all notifications');
  }

  // Public method to add new notification
  addNotification(notification: Omit<Notification, 'id' | 'timestamp'>): void {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date()
    };
    
    this.notifications.unshift(newNotification);
    
    // Auto-hide if specified
    if (notification.autoHide && notification.duration) {
      setTimeout(() => {
        this.removeNotification(newNotification.id, new Event('auto-hide'));
      }, notification.duration);
    }
  }
}
