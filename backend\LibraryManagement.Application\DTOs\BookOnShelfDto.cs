namespace LibraryManagement.Application.DTOs;

public class BookOnShelfDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string ISBN { get; set; } = string.Empty;
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public int OnShelfQuantity { get; set; }
    public int StockQuantity { get; set; } // Đ<PERSON> tương thích với frontend (sẽ chứa OnShelfQuantity)
    public int BookshelfId { get; set; }
    public string BookshelfName { get; set; } = string.Empty;
    public string? LocationCode { get; set; }
    public string Location { get; set; } = string.Empty; // Vị trí đ<PERSON>y đủ cho frontend
}