namespace LibraryManagement.Core.Entities
{
    public class Bookshelf : BaseEntity
    {
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public int ZoneId { get; set; }
        public int Capacity { get; set; }
        public int CurrentCount { get; set; } = 0;
        public string Status { get; set; } = "Active";

        // Navigation properties
        public virtual Zone Zone { get; set; } = null!;
        public virtual ICollection<Book> Books { get; set; } = new List<Book>();
        public virtual ICollection<BookLocation> BookLocations { get; set; } = new List<BookLocation>();
    }
}