using System.ComponentModel.DataAnnotations;

namespace LibraryManagement.Application.DTOs;

public class BookshelfDto
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public int ZoneId { get; set; }
    public string? ZoneName { get; set; }
    public string? Description { get; set; }
    public int Capacity { get; set; }
    public int CurrentCount { get; set; }
    public int AvailableSpace { get; set; }
    public string? Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

public class CreateShelfDto
{
    [Required(ErrorMessage = "Tên kệ là bắt buộc")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "Tên kệ phải từ 2-100 ký tự")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Vui lòng chọn khu vực")]
    [Range(1, int.MaxValue, ErrorMessage = "<PERSON>hu vực không hợp lệ")]
    public int ZoneId { get; set; }

    [StringLength(500, ErrorMessage = "Mô tả không được quá 500 ký tự")]
    public string? Description { get; set; }

    [Required(ErrorMessage = "Sức chứa là bắt buộc")]
    [Range(1, 1000, ErrorMessage = "Sức chứa phải từ 1-1000")]
    public int Capacity { get; set; }

    [StringLength(20, ErrorMessage = "Trạng thái không hợp lệ")]
    public string? Status { get; set; } = "Active";
}

public class UpdateShelfDto : CreateShelfDto
{
    public int Id { get; set; }
}

public class AssignBookToShelfDto
{
    [Required(ErrorMessage = "ID kệ là bắt buộc")]
    public int ShelfId { get; set; }

    [Required(ErrorMessage = "ID sách là bắt buộc")]
    public int BookId { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn 0")]
    public int Quantity { get; set; } = 1;

    [StringLength(10, ErrorMessage = "Mã vị trí không được quá 10 ký tự")]
    public string? LocationCode { get; set; }
}

public class RemoveBookFromShelfDto
{
    [Required(ErrorMessage = "ID kệ là bắt buộc")]
    public int ShelfId { get; set; }

    [Required(ErrorMessage = "ID sách là bắt buộc")]
    public int BookId { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn 0")]
    public int? Quantity { get; set; } // If null, remove all
}

public class RemoveBookLocationDto
{
    [Required(ErrorMessage = "ID BookLocation là bắt buộc")]
    public int BookLocationId { get; set; }
}

public class BookInShelfDto
{
    public int BookId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string? ISBN { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public string? LocationCode { get; set; }
    public int StockQuantity { get; set; }
    public int TotalQuantity { get; set; }
    public string ShelfLocation { get; set; } = string.Empty;
}

public class ShelfLocationsDto
{
    public List<string> AvailableLocations { get; set; } = new();
    public List<string> UsedLocations { get; set; } = new();
    public int Capacity { get; set; }
    public int CurrentCount { get; set; }
}

public class ShelfValidationDto
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

public class ZoneDto
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
}

