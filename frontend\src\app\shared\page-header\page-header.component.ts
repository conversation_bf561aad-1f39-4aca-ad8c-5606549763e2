import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';

export interface PageHeaderAction {
  label: string;
  icon?: string;
  color?: 'primary' | 'accent' | 'warn';
  routerLink?: string;
  click?: () => void;
  disabled?: boolean;
  tooltip?: string;
}

@Component({
  selector: 'app-page-header',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    RouterModule
  ],
  template: `
    <div class="page-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-icon" *ngIf="icon">
            <mat-icon [class]="iconClass">{{ icon }}</mat-icon>
          </div>
          <div class="header-text">
            <h1 class="page-title">{{ title }}</h1>
            <p class="page-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
          </div>
        </div>
        <div class="header-actions" *ngIf="actions.length > 0">
          <ng-container *ngFor="let action of actions">
            <button
              *ngIf="!action.routerLink"
              mat-raised-button
              [color]="action.color || 'primary'"
              [disabled]="action.disabled"
              [matTooltip]="action.tooltip || ''"
              (click)="action.click && action.click()"
              class="action-button">
              <mat-icon *ngIf="action.icon">{{ action.icon }}</mat-icon>
              {{ action.label }}
            </button>
            <button
              *ngIf="action.routerLink"
              mat-raised-button
              [color]="action.color || 'primary'"
              [disabled]="action.disabled"
              [matTooltip]="action.tooltip || ''"
              [routerLink]="action.routerLink"
              class="action-button">
              <mat-icon *ngIf="action.icon">{{ action.icon }}</mat-icon>
              {{ action.label }}
            </button>
          </ng-container>
        </div>
      </div>
      <div class="header-divider"></div>
    </div>
  `,
  styles: [`
    .page-header {
      background: var(--color-surface);
      border-radius: 16px;
      margin-bottom: 24px;
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--color-outline);
      overflow: hidden;
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24px 32px;
      gap: 24px;
    }

    .header-main {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
    }

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: var(--color-primary);
      color: var(--color-on-primary);
      flex-shrink: 0;
    }

    .header-icon mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .header-icon.secondary {
      background: var(--color-secondary);
    }

    .header-icon.success {
      background: var(--color-success);
    }

    .header-icon.warning {
      background: var(--color-warning);
    }

    .header-text {
      flex: 1;
    }

    .page-title {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      color: var(--color-on-surface);
      line-height: 1.2;
    }

    .page-subtitle {
      margin: 4px 0 0 0;
      font-size: 16px;
      color: var(--color-on-surface);
      opacity: 0.7;
      line-height: 1.4;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;
    }

    .action-button {
      border-radius: 12px;
      font-weight: 500;
      text-transform: none;
      padding: 0 20px;
      height: 40px;
      box-shadow: var(--shadow-sm);
    }

    .action-button mat-icon {
      margin-right: 8px;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }

    .header-divider {
      height: 1px;
      background: var(--color-outline);
      opacity: 0.3;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        padding: 20px 24px;
      }

      .header-main {
        width: 100%;
      }

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }

      .page-title {
        font-size: 24px;
      }

      .page-subtitle {
        font-size: 14px;
      }

      .action-button {
        height: 36px;
        padding: 0 16px;
        font-size: 14px;
      }
    }

    @media (max-width: 480px) {
      .header-content {
        padding: 16px 20px;
      }

      .header-main {
        gap: 12px;
      }

      .header-icon {
        width: 40px;
        height: 40px;
      }

      .header-icon mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .page-title {
        font-size: 20px;
      }

      .header-actions {
        flex-wrap: wrap;
        gap: 8px;
      }

      .action-button {
        flex: 1;
        min-width: 120px;
      }
    }

    /* Animation */
    .page-header {
      animation: slideInDown 0.3s ease-out;
    }

    @keyframes slideInDown {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  `]
})
export class PageHeaderComponent {
  @Input() title: string = '';
  @Input() subtitle?: string;
  @Input() icon?: string;
  @Input() iconClass?: string = '';
  @Input() actions: PageHeaderAction[] = [];
}
