-- <PERSON><PERSON><PERSON> để fix identity seeds cho tất cả các bảng
-- <PERSON><PERSON>y script này khi identity seeds bị lệch do sample data

USE LibraryManagementDb;
GO

PRINT 'Bắt đầu fix identity seeds...';

-- Fix Books table
DECLARE @MaxBookId INT;
SELECT @MaxBookId = ISNULL(MAX(Id), 0) FROM Books;
DBCC CHECKIDENT ('Books', RESEED, @MaxBookId);
PRINT 'Fixed Books identity seed to: ' + CAST(@MaxBookId AS VARCHAR(10)) + ' (next ID will be: ' + CAST(@MaxBookId + 1 AS VARCHAR(10)) + ')';

-- Fix Categories table
DECLARE @MaxCategoryId INT;
SELECT @MaxCategoryId = ISNULL(MAX(Id), 0) FROM Categories;
DBCC CHECKIDENT ('Categories', RESEED, @MaxCategoryId);
PRINT 'Fixed Categories identity seed to: ' + CAST(@MaxCategoryId AS VARCHAR(10));

-- Fix Members table
DECLARE @MaxMemberId INT;
SELECT @MaxMemberId = ISNULL(MAX(Id), 0) FROM Members;
DBCC CHECKIDENT ('Members', RESEED, @MaxMemberId);
PRINT 'Fixed Members identity seed to: ' + CAST(@MaxMemberId AS VARCHAR(10));

-- Fix Users table
DECLARE @MaxUserId INT;
SELECT @MaxUserId = ISNULL(MAX(Id), 0) FROM Users;
DBCC CHECKIDENT ('Users', RESEED, @MaxUserId);
PRINT 'Fixed Users identity seed to: ' + CAST(@MaxUserId AS VARCHAR(10));

-- Fix Zones table
DECLARE @MaxZoneId INT;
SELECT @MaxZoneId = ISNULL(MAX(Id), 0) FROM Zones;
DBCC CHECKIDENT ('Zones', RESEED, @MaxZoneId);
PRINT 'Fixed Zones identity seed to: ' + CAST(@MaxZoneId AS VARCHAR(10));

-- Fix Bookshelves table
DECLARE @MaxShelfId INT;
SELECT @MaxShelfId = ISNULL(MAX(Id), 0) FROM Bookshelves;
DBCC CHECKIDENT ('Bookshelves', RESEED, @MaxShelfId);
PRINT 'Fixed Bookshelves identity seed to: ' + CAST(@MaxShelfId AS VARCHAR(10));

-- Fix BorrowRecords table
DECLARE @MaxBorrowId INT;
SELECT @MaxBorrowId = ISNULL(MAX(Id), 0) FROM BorrowRecords;
DBCC CHECKIDENT ('BorrowRecords', RESEED, @MaxBorrowId);
PRINT 'Fixed BorrowRecords identity seed to: ' + CAST(@MaxBorrowId AS VARCHAR(10));

-- Fix BookLocations table
DECLARE @MaxLocationId INT;
SELECT @MaxLocationId = ISNULL(MAX(Id), 0) FROM BookLocations;
DBCC CHECKIDENT ('BookLocations', RESEED, @MaxLocationId);
PRINT 'Fixed BookLocations identity seed to: ' + CAST(@MaxLocationId AS VARCHAR(10));

PRINT 'Hoàn thành fix identity seeds!';

-- Hiển thị kết quả
PRINT '';
PRINT 'Kiểm tra identity seeds hiện tại:';
DBCC CHECKIDENT ('Books');
DBCC CHECKIDENT ('Categories');
DBCC CHECKIDENT ('Members');
DBCC CHECKIDENT ('Users');
DBCC CHECKIDENT ('Zones');
DBCC CHECKIDENT ('Bookshelves');
DBCC CHECKIDENT ('BorrowRecords');
DBCC CHECKIDENT ('BookLocations');
