import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSliderModule } from '@angular/material/slider';

export interface SearchField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'dateRange' | 'number' | 'slider';
  options?: { value: any; label: string }[];
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
}

export interface SearchCriteria {
  [key: string]: any;
}

@Component({
  selector: 'app-advanced-search',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatExpansionModule,
    MatChipsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSliderModule
  ],
  template: `
    <mat-expansion-panel class="search-panel" [expanded]="expanded">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <mat-icon>search</mat-icon>
          <span>Tìm kiếm nâng cao</span>
        </mat-panel-title>
        <mat-panel-description>
          <span *ngIf="hasActiveFilters()" class="active-filters">
            {{ getActiveFiltersCount() }} bộ lọc đang áp dụng
          </span>
          <span *ngIf="!hasActiveFilters()">
            Nhấn để mở rộng tùy chọn tìm kiếm
          </span>
        </mat-panel-description>
      </mat-expansion-panel-header>

      <div class="search-content">
        <form [formGroup]="searchForm" class="search-form">
          <div class="search-grid">
            <ng-container *ngFor="let field of searchFields">
              <!-- Text Input -->
              <mat-form-field 
                *ngIf="field.type === 'text'" 
                appearance="outline" 
                class="search-field">
                <mat-label>{{ field.label }}</mat-label>
                <input 
                  matInput 
                  [formControlName]="field.key"
                  [placeholder]="field.placeholder || ''">
                <mat-icon matSuffix>search</mat-icon>
              </mat-form-field>

              <!-- Select -->
              <mat-form-field 
                *ngIf="field.type === 'select'" 
                appearance="outline" 
                class="search-field">
                <mat-label>{{ field.label }}</mat-label>
                <mat-select [formControlName]="field.key">
                  <mat-option value="">Tất cả</mat-option>
                  <mat-option 
                    *ngFor="let option of field.options" 
                    [value]="option.value">
                    {{ option.label }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <!-- Date -->
              <mat-form-field 
                *ngIf="field.type === 'date'" 
                appearance="outline" 
                class="search-field">
                <mat-label>{{ field.label }}</mat-label>
                <input 
                  matInput 
                  [matDatepicker]="picker"
                  [formControlName]="field.key">
                <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>

              <!-- Number -->
              <mat-form-field
                *ngIf="field.type === 'number'"
                appearance="outline"
                class="search-field">
                <mat-label>{{ field.label }}</mat-label>
                <input
                  matInput
                  type="number"
                  [formControlName]="field.key"
                  [attr.min]="field.min"
                  [attr.max]="field.max"
                  [attr.step]="field.step || 1">
              </mat-form-field>

              <!-- Slider -->
              <div *ngIf="field.type === 'slider'" class="slider-field">
                <label class="slider-label">{{ field.label }}</label>
                <mat-slider 
                  [min]="field.min || 0" 
                  [max]="field.max || 100"
                  [step]="field.step || 1"
                  [formControlName]="field.key"
                  class="search-slider">
                  <input matSliderThumb [formControlName]="field.key">
                </mat-slider>
                <span class="slider-value">{{ searchForm.get(field.key)?.value || 0 }}</span>
              </div>
            </ng-container>
          </div>

          <!-- Active Filters Display -->
          <div *ngIf="hasActiveFilters()" class="active-filters-display">
            <h4>Bộ lọc đang áp dụng:</h4>
            <mat-chip-set>
              <mat-chip 
                *ngFor="let filter of getActiveFilters()" 
                [removable]="true"
                (removed)="removeFilter(filter.key)">
                {{ filter.label }}: {{ filter.displayValue }}
                <mat-icon matChipRemove>cancel</mat-icon>
              </mat-chip>
            </mat-chip-set>
          </div>

          <!-- Action Buttons -->
          <div class="search-actions">
            <button 
              type="button"
              mat-raised-button 
              color="primary" 
              (click)="onSearch()"
              [disabled]="!hasActiveFilters()">
              <mat-icon>search</mat-icon>
              Tìm kiếm
            </button>
            <button 
              type="button"
              mat-stroked-button 
              (click)="onClear()"
              [disabled]="!hasActiveFilters()">
              <mat-icon>clear</mat-icon>
              Xóa bộ lọc
            </button>
            <button 
              type="button"
              mat-button 
              (click)="onSaveSearch()"
              [disabled]="!hasActiveFilters()">
              <mat-icon>bookmark</mat-icon>
              Lưu tìm kiếm
            </button>
          </div>
        </form>
      </div>
    </mat-expansion-panel>
  `,
  styles: [`
    .search-panel {
      margin-bottom: 24px;
      border-radius: 12px;
      box-shadow: var(--shadow-md);
      border: 1px solid var(--color-outline);
    }

    .search-panel .mat-expansion-panel-header {
      padding: 16px 24px;
    }

    .search-panel .mat-expansion-panel-header-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-weight: 600;
    }

    .active-filters {
      color: var(--color-primary);
      font-weight: 500;
    }

    .search-content {
      padding: 0 24px 24px;
    }

    .search-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }

    .search-field {
      width: 100%;
    }

    .slider-field {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 8px 0;
    }

    .slider-label {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-on-surface);
    }

    .search-slider {
      width: 100%;
    }

    .slider-value {
      font-size: 12px;
      color: var(--color-primary);
      font-weight: 600;
      text-align: center;
    }

    .active-filters-display {
      margin-bottom: 24px;
      padding: 16px;
      background: var(--color-surface-variant);
      border-radius: 8px;
    }

    .active-filters-display h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--color-on-surface);
    }

    .search-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      flex-wrap: wrap;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .search-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }

      .search-actions {
        justify-content: stretch;
      }

      .search-actions button {
        flex: 1;
      }
    }

    @media (max-width: 480px) {
      .search-panel .mat-expansion-panel-header {
        padding: 12px 16px;
      }

      .search-content {
        padding: 0 16px 16px;
      }

      .search-actions {
        flex-direction: column;
      }
    }
  `]
})
export class AdvancedSearchComponent implements OnInit {
  @Input() searchFields: SearchField[] = [];
  @Input() expanded: boolean = false;
  @Input() initialValues: SearchCriteria = {};

  @Output() search = new EventEmitter<SearchCriteria>();
  @Output() clear = new EventEmitter<void>();
  @Output() saveSearch = new EventEmitter<SearchCriteria>();

  searchForm: FormGroup;

  constructor(private fb: FormBuilder) {
    this.searchForm = this.fb.group({});
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    const formControls: any = {};
    this.searchFields.forEach(field => {
      formControls[field.key] = [this.initialValues[field.key] || ''];
    });
    this.searchForm = this.fb.group(formControls);
  }

  hasActiveFilters(): boolean {
    const values = this.searchForm.value;
    return Object.keys(values).some(key => {
      const value = values[key];
      return value !== null && value !== undefined && value !== '';
    });
  }

  getActiveFiltersCount(): number {
    const values = this.searchForm.value;
    return Object.keys(values).filter(key => {
      const value = values[key];
      return value !== null && value !== undefined && value !== '';
    }).length;
  }

  getActiveFilters(): Array<{key: string, label: string, displayValue: string}> {
    const values = this.searchForm.value;
    const activeFilters: Array<{key: string, label: string, displayValue: string}> = [];

    this.searchFields.forEach(field => {
      const value = values[field.key];
      if (value !== null && value !== undefined && value !== '') {
        let displayValue = value.toString();
        
        if (field.type === 'select' && field.options) {
          const option = field.options.find(opt => opt.value === value);
          displayValue = option ? option.label : value.toString();
        }

        activeFilters.push({
          key: field.key,
          label: field.label,
          displayValue
        });
      }
    });

    return activeFilters;
  }

  removeFilter(key: string): void {
    this.searchForm.patchValue({ [key]: '' });
    this.onSearch();
  }

  onSearch(): void {
    this.search.emit(this.searchForm.value);
  }

  onClear(): void {
    this.searchForm.reset();
    this.clear.emit();
  }

  onSaveSearch(): void {
    this.saveSearch.emit(this.searchForm.value);
  }
}
