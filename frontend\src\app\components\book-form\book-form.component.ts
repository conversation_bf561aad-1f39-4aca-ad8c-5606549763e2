import { Component } from "@angular/core";
import { CommonModule } from "@angular/common";
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  AbstractControl,
  ValidationErrors,
} from "@angular/forms";
import { MatCardModule } from "@angular/material/card";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from "@angular/material/select";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { MatSnackBar, MatSnackBarModule } from "@angular/material/snack-bar";
import { MatStepperModule } from "@angular/material/stepper";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatNativeDateModule } from "@angular/material/core";
import { MatDividerModule } from "@angular/material/divider";
import { MatChipsModule } from "@angular/material/chips";
import { MatProgressBarModule } from "@angular/material/progress-bar";
import { BookService } from "../../services/book.service";
import { Book } from "src/app/models/book.model";
import { Category } from "src/app/models/category.model";
import { CategoryService } from "../../services/category.service";
import { ShelfService } from "../../services/shelf.service";
import { ZoneService, Zone } from "../../services/zone.service";
import { Shelf } from "src/app/models/shelf.model";
import { ActivatedRoute, Router } from "@angular/router";
import { PageContainerComponent } from "../../shared/page-container/page-container.component";
import { FormContainerComponent, FormAction } from "../../shared/form-container/form-container.component";

@Component({
  selector: "app-book-form",
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatStepperModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDividerModule,
    MatChipsModule,
    MatProgressBarModule,
    PageContainerComponent,
    FormContainerComponent,
  ],
  template: `
    <app-page-container>
      <app-form-container
        [title]="isEditMode ? 'Chỉnh sửa sách' : 'Thêm sách mới'"
        [subtitle]="isEditMode ? 'Cập nhật thông tin sách trong thư viện' : 'Thêm một cuốn sách mới vào bộ sưu tập'"
        [icon]="isEditMode ? 'edit_note' : 'library_add'"
        [isLoading]="isLoading"
        [showCloseButton]="true"
        [actions]="formActions"
        (close)="onCancel()">

        <form [formGroup]="bookForm" (ngSubmit)="onSubmit()" class="book-form">

              <!-- Basic Information Card -->
              <div class="form-card">
                <div class="card-header">
                  <div class="card-icon">
                    <mat-icon>menu_book</mat-icon>
                  </div>
                  <div class="card-title">
                    <h3>Thông tin cơ bản</h3>
                    <p>Thông tin chính về cuốn sách</p>
                  </div>
                </div>

                <div class="card-content">
                  <div class="form-group">
                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Tên sách</mat-label>
                      <input
                        matInput
                        formControlName="title"
                        placeholder="Nhập tên sách..."
                      />
                      <mat-icon matSuffix color="primary">book</mat-icon>
                      <mat-error
                        *ngIf="bookForm.get('title')?.hasError('required')"
                      >
                        Tên sách là bắt buộc
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Tác giả</mat-label>
                      <input
                        matInput
                        formControlName="author"
                        placeholder="Tên tác giả..."
                      />
                      <mat-icon matSuffix color="primary">person</mat-icon>
                      <mat-error
                        *ngIf="bookForm.get('author')?.hasError('required')"
                      >
                        Tác giả là bắt buộc
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Thể loại</mat-label>
                      <mat-select
                        formControlName="categoryId"
                        placeholder="Chọn thể loại..."
                      >
                        <mat-option
                          *ngFor="let category of categories"
                          [value]="category.id"
                        >
                          <div class="category-option">
                            <mat-icon>label</mat-icon>
                            <span>{{ category.name }}</span>
                          </div>
                        </mat-option>
                      </mat-select>
                      <mat-icon matSuffix color="primary">category</mat-icon>
                      <mat-error
                        *ngIf="bookForm.get('categoryId')?.hasError('required')"
                      >
                        Thể loại là bắt buộc
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <!-- Publication Details Card -->
              <div class="form-card">
                <div class="card-header">
                  <div class="card-icon">
                    <mat-icon>publish</mat-icon>
                  </div>
                  <div class="card-title">
                    <h3>Chi tiết xuất bản</h3>
                    <p>Thông tin về nhà xuất bản và phát hành</p>
                  </div>
                </div>

                <div class="card-content">
                  <div class="form-row">
                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Mã ISBN</mat-label>
                      <input
                        matInput
                        formControlName="isbn"
                        placeholder="978-0-123456-78-9"
                      />
                      <mat-icon matSuffix color="primary">qr_code</mat-icon>
                      <mat-hint>ISBN-10 hoặc ISBN-13</mat-hint>
                      <mat-error
                        *ngIf="bookForm.get('isbn')?.hasError('invalidIsbn')"
                      >
                        Mã ISBN không hợp lệ
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Nhà xuất bản</mat-label>
                      <input
                        matInput
                        formControlName="publisher"
                        placeholder="Tên nhà xuất bản..."
                      />
                      <mat-icon matSuffix color="primary">business</mat-icon>
                    </mat-form-field>
                  </div>

                  <div class="form-row">
                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Ngày xuất bản</mat-label>
                      <input
                        matInput
                        [matDatepicker]="picker"
                        formControlName="publishedDate"
                      />
                      <mat-datepicker-toggle matIconSuffix [for]="picker">
                        <mat-icon matDatepickerToggleIcon>event</mat-icon>
                      </mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Giá bán</mat-label>
                      <input
                        matInput
                        type="number"
                        formControlName="price"
                        placeholder="0"
                        min="0"
                        step="1000"
                      />
                      <span matTextPrefix>₫&nbsp;</span>
                      <mat-icon matSuffix color="primary">payments</mat-icon>
                      <mat-hint>Để trống nếu không bán</mat-hint>
                    </mat-form-field>
                  </div>
                </div>
              </div>

              <!-- Library Management Card -->
              <div class="form-card">
                <div class="card-header">
                  <div class="card-icon">
                    <mat-icon>inventory_2</mat-icon>
                  </div>
                  <div class="card-title">
                    <h3>Quản lý thư viện</h3>
                    <p>Thông tin về số lượng và trạng thái</p>
                  </div>
                </div>

                <div class="card-content">
                  <div class="form-row">
                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Số lượng</mat-label>
                      <input
                        matInput
                        type="number"
                        formControlName="quantity"
                        min="0"
                        placeholder="0"
                      />
                      <mat-icon matSuffix color="primary"
                        >format_list_numbered</mat-icon
                      >
                      <mat-hint>Số bản sách có trong thư viện</mat-hint>
                      <mat-error
                        *ngIf="bookForm.get('quantity')?.hasError('required')"
                      >
                        Số lượng là bắt buộc
                      </mat-error>
                    </mat-form-field>

                    <div class="status-display">
                      <div
                        class="status-badge"
                        [ngClass]="getAvailabilityClass()"
                      >
                        <mat-icon>{{ getAvailabilityIcon() }}</mat-icon>
                        <div class="status-info">
                          <span class="status-text">{{
                            getAvailabilityText()
                          }}</span>
                          <small class="status-subtitle"
                            >Trạng thái hiện tại</small
                          >
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Shelf Location Section -->
                  <div class="form-row">
                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Kệ sách (tùy chọn)</mat-label>
                      <mat-select formControlName="bookshelfId">
                        <mat-option [value]="null">Không chỉ định kệ</mat-option>
                        <mat-option *ngFor="let shelf of shelves" [value]="shelf.id">
                          <div class="shelf-option">
                            <span>{{ shelf.name }}</span>
                            <small> - {{ getZoneName(shelf.zoneId) }}</small>
                          </div>
                        </mat-option>
                      </mat-select>
                      <mat-icon matSuffix color="primary">shelves</mat-icon>
                      <mat-hint>Chọn kệ để đặt sách</mat-hint>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="elegant-field">
                      <mat-label>Vị trí (tùy chọn)</mat-label>
                      <input
                        matInput
                        formControlName="locationCode"
                        placeholder="VD: A1, B2..."
                      />
                      <mat-icon matSuffix color="primary">place</mat-icon>
                      <mat-hint>Vị trí cụ thể trên kệ</mat-hint>
                    </mat-form-field>
                  </div>

                  <div class="form-group">
                    <mat-form-field
                      appearance="outline"
                      class="elegant-field description-field"
                    >
                      <mat-label>Mô tả sách</mat-label>
                      <textarea
                        matInput
                        formControlName="description"
                        rows="4"
                        placeholder="Mô tả ngắn gọn về nội dung, đặc điểm của sách..."
                      ></textarea>
                      <mat-icon matSuffix color="primary">description</mat-icon>
                      <mat-hint
                        >Thông tin thêm giúp độc giả hiểu về sách</mat-hint
                      >
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </form>
          </div>

          <!-- Right Column - Image Upload -->
          <div class="sidebar">
            <div class="form-card image-card">
              <div class="card-header">
                <div class="card-icon">
                  <mat-icon>image</mat-icon>
                </div>
                <div class="card-title">
                  <h3>Hình ảnh bìa</h3>
                  <p>Tải lên ảnh bìa sách</p>
                </div>
              </div>

              <div class="card-content">
                <div class="image-upload-container">
                  <!-- Upload Area -->
                  <div
                    class="upload-zone"
                    [class.has-image]="previewUrl"
                    (click)="triggerFileInput()"
                    (dragover)="onDragOver($event)"
                    (dragleave)="onDragLeave($event)"
                    (drop)="onDrop($event)"
                  >
                    <!-- No Image State -->
                    <div *ngIf="!previewUrl" class="upload-empty">
                      <div class="upload-icon-container">
                        <mat-icon class="upload-icon">cloud_upload</mat-icon>
                      </div>
                      <h4>Tải lên hình ảnh</h4>
                      <p>Kéo thả file hoặc nhấp để chọn</p>
                      <div class="upload-specs">
                        <span class="spec">JPG, PNG, GIF</span>
                        <span class="spec">Tối đa 5MB</span>
                      </div>
                    </div>

                    <!-- Image Preview -->
                    <div *ngIf="previewUrl" class="image-preview-container">
                      <div class="image-preview">
                        <img [src]="previewUrl" alt="Book cover preview" />
                        <div class="image-actions">
                          <button
                            mat-fab
                            color="primary"
                            (click)="
                              triggerFileInput(); $event.stopPropagation()
                            "
                          >
                            <mat-icon>edit</mat-icon>
                          </button>
                          <button
                            mat-fab
                            color="warn"
                            (click)="removeImage(); $event.stopPropagation()"
                          >
                            <mat-icon>delete</mat-icon>
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Hidden File Input -->
                    <input
                      #fileInput
                      type="file"
                      (change)="onImageSelected($event)"
                      accept="image/*"
                      style="display: none;"
                    />
                  </div>

                  <!-- Upload Progress -->
                  <div
                    *ngIf="uploadProgress > 0 && uploadProgress < 100"
                    class="upload-progress-container"
                  >
                    <div class="progress-info">
                      <span>Đang tải lên...</span>
                      <span class="progress-percent"
                        >{{ uploadProgress }}%</span
                      >
                    </div>
                    <mat-progress-bar
                      mode="determinate"
                      [value]="uploadProgress"
                      class="elegant-progress"
                    ></mat-progress-bar>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
              <button
                mat-stroked-button
                color="accent"
                (click)="resetForm()"
                [disabled]="isLoading"
                *ngIf="!isEditMode"
              >
                <mat-icon>refresh</mat-icon>
                Làm mới
              </button>
            </div>
          </div>
        </div>

        <!-- Action Bar -->
        <div class="action-bar">
          <div class="action-left">
            <button
              mat-button
              (click)="onCancel()"
              [disabled]="isLoading"
              class="cancel-btn"
            >
              <mat-icon>arrow_back</mat-icon>
              Quay lại
            </button>
          </div>

          <div class="action-right">
            <button
              mat-raised-button
              color="primary"
              (click)="onSubmit()"
              [disabled]="!bookForm.valid || isLoading"
              class="submit-btn"
            >
              <mat-icon *ngIf="!isLoading">{{
                isEditMode ? "save" : "add_circle"
              }}</mat-icon>
              <mat-spinner
                diameter="20"
                *ngIf="isLoading"
                class="btn-spinner"
              ></mat-spinner>
              <span>{{
                isLoading
                  ? "Đang xử lý..."
                  : isEditMode
                  ? "Cập nhật sách"
                  : "Thêm vào thư viện"
              }}</span>
        </form>
      </app-form-container>
    </app-page-container>
  `,
  styles: [
    `
      .page-container {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 0;
      }

      .page-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding: 24px 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        display: flex;
        align-items: center;
        gap: 20px;
      }

      .header-icon {
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
      }

      .header-icon mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
        color: white;
      }

      .header-text h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: #2d3748;
        letter-spacing: -0.5px;
      }

      .header-text p {
        margin: 0;
        color: #718096;
        font-size: 16px;
        font-weight: 400;
      }

      .close-btn {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 12px;
      }

      .content-area {
        padding: 32px;
        max-width: 1400px;
        margin: 0 auto;
      }

      .form-grid {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 32px;
        align-items: start;
      }

      .main-form {
        display: flex;
        flex-direction: column;
        gap: 80px;
      }

      .sidebar {
        display: flex;
        flex-direction: column;
        gap: 24px;
        position: sticky;
        top: 32px;
      }

      .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
        border: 1px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        margin-bottom: 20px;
      }

      .form-card:hover {
        box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .card-header {
        padding: 24px 32px 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.05),
          rgba(118, 75, 162, 0.05)
        );
      }

      .card-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
      }

      .card-icon mat-icon {
        color: white;
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .card-title h3 {
        margin: 0 0 4px 0;
        font-size: 20px;
        font-weight: 600;
        color: #2d3748;
        letter-spacing: -0.3px;
      }

      .card-title p {
        margin: 0;
        color: #718096;
        font-size: 14px;
        font-weight: 400;
      }

      .card-content {
        padding: 20px 28px 28px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 20px;
      }

      .elegant-field {
        width: 100%;
      }

      .elegant-field .mat-mdc-form-field-outline {
        border-radius: 16px !important;
        border-width: 2px !important;
      }

      .elegant-field.mat-focused .mat-mdc-form-field-outline-thick {
        border-color: #667eea !important;
        border-width: 2px !important;
      }

      .elegant-field .mat-mdc-form-field-label {
        font-weight: 500;
        color: #4a5568;
      }

      .category-option {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 4px 0;
      }

      .category-option mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: #667eea;
      }

      .status-display {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .status-badge {
        padding: 12px 16px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 10px;
        min-width: 160px;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: default;
      }

      .status-badge.available {
        background: linear-gradient(135deg, #48bb78, #38a169);
        color: white;
        box-shadow: 0 4px 20px rgba(72, 187, 120, 0.3);
      }

      .status-badge.limited {
        background: linear-gradient(135deg, #ed8936, #dd6b20);
        color: white;
        box-shadow: 0 4px 20px rgba(237, 137, 54, 0.3);
      }

      .status-badge.unavailable {
        background: linear-gradient(135deg, #f56565, #e53e3e);
        color: white;
        box-shadow: 0 4px 20px rgba(245, 101, 101, 0.3);
      }

      .status-info {
        display: flex;
        flex-direction: column;
      }

      .status-text {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 2px;
      }

      .status-subtitle {
        font-size: 11px;
        opacity: 0.9;
        font-weight: 400;
      }

      .description-field {
        width: 100%;
        max-width: 100% !important;
      }

      .description-field textarea {
        resize: vertical;
        min-height: 100px;
      }

      .image-card {
        background: rgba(255, 255, 255, 0.98);
      }

      .image-upload-container {
        margin-top: 4px;
      }

      .upload-zone {
        border: 3px dashed #cbd5e0;
        border-radius: 20px;
        position: relative;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        background: rgba(247, 250, 252, 0.8);
      }

      .upload-zone:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.02);
        transform: scale(1.02);
      }

      .upload-zone.has-image {
        border: none;
        background: transparent;
        padding: 0;
      }

      .upload-empty {
        padding: 48px 24px;
        text-align: center;
      }

      .upload-icon-container {
        margin-bottom: 20px;
      }

      .upload-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #667eea;
        opacity: 0.8;
      }

      .upload-empty h4 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
      }

      .upload-empty p {
        margin: 0 0 20px 0;
        color: #718096;
        font-size: 14px;
      }

      .upload-specs {
        display: flex;
        gap: 12px;
        justify-content: center;
        flex-wrap: wrap;
      }

      .spec {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }

      .image-preview-container {
        position: relative;
      }

      .image-preview {
        position: relative;
        border-radius: 20px;
        overflow: hidden;
        group: image-preview;
      }

      .image-preview img {
        width: 100%;
        height: 320px;
        object-fit: cover;
        display: block;
        transition: transform 0.3s ease;
      }

      .image-preview:hover img {
        transform: scale(1.05);
      }

      .image-actions {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        gap: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 10;
      }

      .image-preview:hover .image-actions {
        opacity: 1;
      }

      .image-actions::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 140px;
        height: 60px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 30px;
        z-index: -1;
        backdrop-filter: blur(10px);
      }

      .upload-progress-container {
        margin-top: 16px;
        padding: 16px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 12px;
      }

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 500;
        color: #4a5568;
      }

      .progress-percent {
        color: #667eea;
        font-weight: 600;
      }

      .elegant-progress {
        height: 6px;
        border-radius: 3px;
      }

      .elegant-progress .mat-mdc-progress-bar-fill::after {
        background: linear-gradient(135deg, #667eea, #764ba2);
      }

      .quick-actions {
        display: flex;
        gap: 12px;
      }

      .quick-actions button {
        border-radius: 12px;
        font-weight: 500;
      }

      .action-bar {
        margin-top: 40px;
        padding: 24px 32px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .cancel-btn {
        border-radius: 12px;
        font-weight: 500;
        color: #718096;
      }

      .submit-btn {
        border-radius: 16px;
        padding: 12px 32px;
        font-weight: 600;
        font-size: 16px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .submit-btn:hover:not(:disabled) {
        box-shadow: 0 12px 40px rgba(102, 126, 234, 0.5);
        transform: translateY(-2px);
      }

      .submit-btn .btn-spinner {
        margin-right: 8px;
      }

      .progress-container {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 32px;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .progress-content {
        display: flex;
        align-items: center;
        gap: 16px;
        font-weight: 500;
        color: #4a5568;
      }

      /* Responsive Design */
      @media (max-width: 1200px) {
        .form-grid {
          grid-template-columns: 1fr;
          gap: 24px;
        }

        .sidebar {
          position: relative;
          top: auto;
        }

        .main-form {
          gap: 48px;
        }
      }

      @media (max-width: 900px) {
        .form-row {
          grid-template-columns: 1fr;
          gap: 14px;
        }
      }

      @media (max-width: 768px) {
        .content-area {
          padding: 16px;
        }

        .page-header {
          padding: 16px 20px;
          flex-direction: column;
          gap: 16px;
          text-align: center;
        }

        .header-content {
          flex-direction: column;
          text-align: center;
          gap: 12px;
        }

        .header-icon {
          width: 56px;
          height: 56px;
        }

        .header-text h1 {
          font-size: 24px;
        }

        .card-header {
          padding: 20px 24px 16px;
        }

        .card-content {
          padding: 20px 24px 24px;
        }

        .form-row {
          grid-template-columns: 1fr;
          gap: 12px;
        }

        .action-bar {
          flex-direction: column;
          gap: 16px;
          text-align: center;
        }

        .action-left,
        .action-right {
          width: 100%;
        }

        .submit-btn {
          width: 100%;
          justify-content: center;
        }
      }

      /* Animations */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .form-card {
        animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      }

      .form-card:nth-child(1) {
        animation-delay: 0.1s;
      }
      .form-card:nth-child(2) {
        animation-delay: 0.2s;
      }
      .form-card:nth-child(3) {
        animation-delay: 0.3s;
      }
      .image-card {
        animation-delay: 0.4s;
      }
      .action-bar {
        animation-delay: 0.5s;
      }
    `,
  ],
})
export class BookFormComponent {
  bookForm: FormGroup;
  categories: Category[] = [];
  zones: Zone[] = [];
  shelves: Shelf[] = [];
  bookId: number | null = null;
  previewUrl: string | ArrayBuffer | null = null;
  isLoading = false;
  uploadProgress = 0;
  isEditMode = false;
  book: Book | null = null;
  formActions: FormAction[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private bookService: BookService,
    private categoryService: CategoryService,
    private shelfService: ShelfService,
    private zoneService: ZoneService,
    private snackBar: MatSnackBar
  ) {
    this.bookForm = this.fb.group({
      title: ["", [Validators.required, Validators.minLength(2)]],
      author: ["", [Validators.required, Validators.minLength(2)]],
      isbn: ["", isbnValidator],
      publisher: [""],
      publishedDate: [""],
      categoryId: [null, Validators.required],
      quantity: [0, [Validators.required, Validators.min(0)]],
      description: [""],
      imageUrl: [""],
      price: [0, [Validators.min(0)]],
      bookshelfId: [null],
      locationCode: [""],
    });
  }

  ngOnInit() {
    this.loadCategories();
    this.loadZones();
    this.loadShelves();
    this.checkEditMode();
    this.initializeFormActions();
  }

  private initializeFormActions(): void {
    this.formActions = [
      {
        label: 'Hủy',
        type: 'button',
        icon: 'arrow_back',
        click: () => this.onCancel()
      },
      {
        label: this.isEditMode ? 'Cập nhật' : 'Thêm sách',
        type: 'submit',
        color: 'primary',
        icon: this.isEditMode ? 'save' : 'add',
        disabled: this.bookForm?.invalid || this.isLoading
      }
    ];
  }

  private loadCategories() {
    this.categoryService.getCategories().subscribe({
      next: (data) => {
        this.categories = data;
      },
      error: (err: any) => {
        console.error("Error fetching categories:", err);
        this.showMessage("Lỗi khi tải danh sách thể loại", "error");
      },
    });
  }

  private loadZones() {
    this.zoneService.getAllZones().subscribe({
      next: (data) => {
        this.zones = data;
      },
      error: (err: any) => {
        console.error("Error fetching zones:", err);
        this.showMessage("Lỗi khi tải danh sách khu vực", "error");
      },
    });
  }

  private loadShelves() {
    this.shelfService.getAllShelves().subscribe({
      next: (data) => {
        this.shelves = data;
      },
      error: (err: any) => {
        console.error("Error fetching shelves:", err);
        this.showMessage("Lỗi khi tải danh sách kệ sách", "error");
      },
    });
  }

  private checkEditMode() {
    const id = this.route.snapshot.paramMap.get("id");
    if (id) {
      this.bookId = Number(id);
      this.isEditMode = true;
      this.loadBookData();
    }
  }

  private loadBookData() {
    if (!this.bookId) return;

    this.isLoading = true;
    this.bookService.getBook(this.bookId).subscribe({
      next: (book) => {
        const formattedBook = {
          ...book,
          publishedDate: book.publishedDate
            ? new Date(book.publishedDate)
            : null,
        };

        this.book = book;
        this.bookForm.patchValue(formattedBook);
        this.previewUrl = book.imageUrl || null;
        this.isLoading = false;
      },
      error: (err: any) => {
        console.error("Error fetching book:", err);
        this.showMessage("Lỗi khi tải thông tin sách", "error");
        this.router.navigate(["/books"]);
        this.isLoading = false;
      },
    });
  }

  onSubmit() {
    if (!this.bookForm.valid) {
      this.bookForm.markAllAsTouched();
      this.showMessage("Vui lòng kiểm tra lại thông tin đã nhập", "error");
      return;
    }

    const formValue = this.bookForm.value;
    const newQuantity = formValue.quantity;

    if (this.isEditMode && this.book) {
      const oldQuantity = this.book.quantity;
      const stockQuantity = this.book.stockQuantity;

      const delta = newQuantity - oldQuantity;

      if (delta < 0) {
        const decreaseAmount = -delta;
        if (stockQuantity < decreaseAmount) {
          this.showMessage(
            `Không thể giảm số lượng xuống ${newQuantity} vì hiện chỉ còn ${stockQuantity} sách có sẵn trong kho.`,
            "error"
          );
          return;
        }
      }
    }

    this.isLoading = true;

    const bookData = {
      ...formValue,
      stockQuantity: formValue.quantity, // Map quantity thành stockQuantity
      publishedDate: formValue.publishedDate ? formValue.publishedDate : null,
    };

    if (this.isEditMode) {
      this.bookService.updateBook(this.bookId!, bookData).subscribe({
        next: () => {
          this.showMessage("Cập nhật sách thành công!", "success");
          this.router.navigate(["/books"]);
        },
        error: (err: any) => {
          console.error("Error updating book:", err);
          const errorMessage = err.error?.message || "Lỗi khi cập nhật sách";
          this.showMessage(errorMessage, "error");
          this.isLoading = false;
        },
      });
    } else {
      // Khi tạo mới, số có sẵn = tổng số lượng
      this.bookService.createBook(bookData).subscribe({
        next: () => {
          this.showMessage("Thêm sách thành công!", "success");
          this.router.navigate(["/books"]);
        },
        error: (err: any) => {
          console.error("Error creating book:", err);
          const errorMessage = err.error?.message || "Lỗi khi thêm sách";
          this.showMessage(errorMessage, "error");
          this.isLoading = false;
        },
      });
    }
  }

  triggerFileInput() {
    const fileInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement;
    fileInput?.click();
  }

  onImageSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file
      if (!this.validateFile(file)) {
        return;
      }

      // Show preview
      const reader = new FileReader();
      reader.onload = () => {
        this.previewUrl = reader.result;
      };
      reader.readAsDataURL(file);

      // Upload image
      this.uploadImage(file);
    }
  }

  private validateFile(file: File): boolean {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];

    if (!allowedTypes.includes(file.type)) {
      this.showMessage("Chỉ chấp nhận file ảnh (JPG, PNG, GIF, WEBP)", "error");
      return false;
    }

    if (file.size > maxSize) {
      this.showMessage("Kích thước file không được vượt quá 5MB", "error");
      return false;
    }

    return true;
  }

  private uploadImage(file: File) {
    const formData = new FormData();
    formData.append("image", file);

    this.uploadProgress = 10;

    this.bookService.uploadImage(formData).subscribe({
      next: (res) => {
        this.uploadProgress = 100;
        this.bookForm.patchValue({ imageUrl: res.imageUrl });
        this.showMessage("Tải ảnh lên thành công!", "success");

        setTimeout(() => {
          this.uploadProgress = 0;
        }, 1000);
      },
      error: (err: any) => {
        console.error("Error uploading image:", err);
        this.showMessage("Lỗi khi tải ảnh lên", "error");
        this.uploadProgress = 0;
        this.previewUrl = null;
      },
    });
  }

  removeImage() {
    this.previewUrl = null;
    this.bookForm.patchValue({ imageUrl: "" });
    this.showMessage("Đã xóa ảnh", "info");
  }

  resetForm() {
    this.bookForm.reset();
    this.previewUrl = null;
    this.uploadProgress = 0;
    this.showMessage("Đã làm mới form", "info");
  }

  onCancel() {
    this.router.navigate(["/books"]);
  }

  getAvailabilityClass(): string {
    const quantity = this.bookForm.get("quantity")?.value || 0;
    if (quantity === 0) return "unavailable";
    if (quantity <= 5) return "limited";
    return "available";
  }

  getAvailabilityIcon(): string {
    const quantity = this.bookForm.get("quantity")?.value || 0;
    if (quantity === 0) return "block";
    if (quantity <= 5) return "warning";
    return "check_circle";
  }

  getAvailabilityText(): string {
    const quantity = this.bookForm.get("quantity")?.value || 0;
    if (quantity === 0) return "Hết sách";
    if (quantity <= 5) return `Sắp hết (${quantity} cuốn)`;
    return `Còn nhiều (${quantity} cuốn)`;
  }

  getZoneName(zoneId: number): string {
    const zone = this.zones.find(z => z.id === zoneId);
    return zone ? zone.name : 'Không rõ';
  }

  private showMessage(message: string, type: "success" | "error" | "info") {
    this.snackBar.open(message, "Đóng", {
      duration: type === "error" ? 5000 : 3000,
      panelClass: [`snackbar-${type}`],
      horizontalPosition: "center",
      verticalPosition: "top",
    });
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      const file = files[0];

      if (!this.validateFile(file)) {
        return;
      }

      // Show preview
      const reader = new FileReader();
      reader.onload = () => {
        this.previewUrl = reader.result;
      };
      reader.readAsDataURL(file);

      // Upload image
      this.uploadImage(file);
    }
  }
}

export function isbnValidator(
  control: AbstractControl
): ValidationErrors | null {
  const rawValue = control.value;
  if (!rawValue) return null;

  const value = rawValue.replace(/[-\s]/g, ""); // Bỏ gạch, khoảng trắng

  const isISBN10 = /^[0-9]{9}[0-9Xx]$/.test(value);
  const isISBN13 = /^[0-9]{13}$/.test(value);

  if (isISBN10) {
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += (i + 1) * parseInt(value[i]);
    }
    const checkDigit = value[9].toUpperCase() === "X" ? 10 : parseInt(value[9]);
    sum += 10 * checkDigit;

    if (sum % 11 === 0) return null; // hợp lệ ISBN-10
  }

  if (isISBN13) {
    let sum = 0;
    for (let i = 0; i < 12; i++) {
      const digit = parseInt(value[i]);
      sum += i % 2 === 0 ? digit : digit * 3;
    }
    const checkDigit = (10 - (sum % 10)) % 10;
    if (checkDigit === parseInt(value[12])) return null; // hợp lệ ISBN-13
  }

  return { invalidIsbn: true }; // nếu không hợp lệ
}
