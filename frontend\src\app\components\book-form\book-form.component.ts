import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { Router, ActivatedRoute } from '@angular/router';
import { BookService } from '../../services/book.service';
import { CategoryService } from '../../services/category.service';
import { Category } from '../../models/category.model';
import { PageContainerComponent } from "../../shared/page-container/page-container.component";
import { FormContainerComponent, FormAction } from "../../shared/form-container/form-container.component";

@Component({
  selector: 'app-book-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    MatSnackBarModule,
    PageContainerComponent,
    FormContainerComponent,
  ],
  template: `
    <app-page-container>
      <app-form-container
        [title]="isEditMode ? 'Chỉnh sửa sách' : 'Thêm sách mới'"
        [subtitle]="isEditMode ? 'Cập nhật thông tin sách trong thư viện' : 'Thêm một cuốn sách mới vào bộ sưu tập'"
        [icon]="isEditMode ? 'edit_note' : 'library_add'"
        [isLoading]="isLoading"
        [showCloseButton]="true"
        [actions]="formActions"
        (close)="onCancel()">
        
        <form [formGroup]="bookForm" (ngSubmit)="onSubmit()" class="book-form">
          <!-- Basic Information -->
          <div class="form-section">
            <h3>Thông tin cơ bản</h3>
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Tiêu đề sách</mat-label>
                <input matInput formControlName="title" placeholder="Nhập tiêu đề sách">
                <mat-error *ngIf="bookForm.get('title')?.hasError('required')">
                  Tiêu đề sách là bắt buộc
                </mat-error>
              </mat-form-field>
              
              <mat-form-field appearance="outline">
                <mat-label>Tác giả</mat-label>
                <input matInput formControlName="author" placeholder="Nhập tên tác giả">
                <mat-error *ngIf="bookForm.get('author')?.hasError('required')">
                  Tác giả là bắt buộc
                </mat-error>
              </mat-form-field>
            </div>
            
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Thể loại</mat-label>
                <mat-select formControlName="categoryId" placeholder="Chọn thể loại">
                  <mat-option *ngFor="let category of categories" [value]="category.id">
                    {{ category.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="bookForm.get('categoryId')?.hasError('required')">
                  Thể loại là bắt buộc
                </mat-error>
              </mat-form-field>
              
              <mat-form-field appearance="outline">
                <mat-label>Số lượng</mat-label>
                <input matInput type="number" formControlName="totalQuantity" placeholder="Nhập số lượng">
                <mat-error *ngIf="bookForm.get('totalQuantity')?.hasError('required')">
                  Số lượng là bắt buộc
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </form>
      </app-form-container>
    </app-page-container>
  `,
  styles: [`
    .book-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }
    
    .form-section {
      background: var(--color-surface);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      border: 1px solid var(--color-outline);
    }
    
    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-md);
    }
    
    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class BookFormComponent implements OnInit {
  bookForm: FormGroup;
  categories: Category[] = [];
  isEditMode = false;
  isLoading = false;
  formActions: FormAction[] = [];

  constructor(
    private fb: FormBuilder,
    private bookService: BookService,
    private categoryService: CategoryService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.bookForm = this.createForm();
    this.initializeFormActions();
  }

  ngOnInit() {
    this.loadCategories();
    this.checkEditMode();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', Validators.required],
      author: ['', Validators.required],
      categoryId: ['', Validators.required],
      totalQuantity: [1, [Validators.required, Validators.min(1)]]
    });
  }

  private initializeFormActions(): void {
    this.formActions = [
      {
        label: 'Hủy',
        type: 'button',
        icon: 'arrow_back',
        click: () => this.onCancel()
      },
      {
        label: this.isEditMode ? 'Cập nhật' : 'Thêm sách',
        type: 'submit',
        color: 'primary',
        icon: this.isEditMode ? 'save' : 'add',
        disabled: this.bookForm?.invalid || this.isLoading
      }
    ];
  }

  private loadCategories(): void {
    this.categoryService.getCategories().subscribe({
      next: (categories) => {
        this.categories = categories;
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      // Load book data for editing
    }
  }

  onSubmit(): void {
    if (this.bookForm.valid) {
      this.isLoading = true;
      const formData = this.bookForm.value;
      
      if (this.isEditMode) {
        // Update book logic
        this.snackBar.open('Cập nhật sách thành công!', 'Đóng', { duration: 3000 });
      } else {
        // Create book logic
        this.snackBar.open('Thêm sách thành công!', 'Đóng', { duration: 3000 });
      }
      
      this.isLoading = false;
      this.router.navigate(['/books']);
    }
  }

  onCancel(): void {
    this.router.navigate(['/books']);
  }
}
