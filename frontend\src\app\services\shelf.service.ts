import { Injectable } from "@angular/core";
import { HttpClient, HttpErrorResponse } from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { Book } from "src/app/models/book.model";
import {
  Shelf,
  CreateShelf,
  UpdateShelf,
  AssignBookToShelf,
  RemoveBookFromShelf,
  BookInShelf,
  ShelfLocations,
  ShelfValidation,
  ShelfStatus
} from "src/app/models/shelf.model";
import { environment } from "src/environments/environment";

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  errors?: string[];
}

@Injectable({ providedIn: "root" })
export class ShelfService {
  private readonly apiUrl = `${environment.apiUrl}/shelves`;

  constructor(private http: HttpClient) {}

  // Get all shelves with enhanced error handling
  getAllShelves(): Observable<Shelf[]> {
    return this.http.get<Shelf[]>(this.apiUrl).pipe(
      map(shelves => shelves.map(shelf => ({
        ...shelf,
        availableSpace: shelf.capacity - shelf.currentCount,
        status: shelf.status as ShelfStatus
      }))),
      catchError(this.handleError)
    );
  }

  // Get shelf by ID with full details
  getShelfById(id: number): Observable<Shelf> {
    return this.http.get<Shelf>(`${this.apiUrl}/${id}`).pipe(
      map(shelf => ({
        ...shelf,
        availableSpace: shelf.capacity - shelf.currentCount,
        status: shelf.status as ShelfStatus
      })),
      catchError(this.handleError)
    );
  }

  // Create new shelf with validation
  createShelf(shelf: CreateShelf): Observable<ApiResponse<{ shelfId: number }>> {
    return this.http.post<ApiResponse<{ shelfId: number }>>(this.apiUrl, shelf).pipe(
      catchError(this.handleError)
    );
  }

  // Update existing shelf
  updateShelf(id: number, shelf: UpdateShelf): Observable<ApiResponse<void>> {
    return this.http.put<ApiResponse<void>>(`${this.apiUrl}/${id}`, shelf).pipe(
      catchError(this.handleError)
    );
  }

  // Delete shelf with cascade check
  deleteShelf(id: number): Observable<ApiResponse<void>> {
    return this.http.delete<ApiResponse<void>>(`${this.apiUrl}/${id}`).pipe(
      catchError(this.handleError)
    );
  }

  // Get books in specific shelf
  getBooksInShelf(shelfId: number): Observable<BookInShelf[]> {
    return this.http.get<BookInShelf[]>(`${this.apiUrl}/${shelfId}/books`).pipe(
      catchError(this.handleError)
    );
  }

  // Assign book to shelf with quantity
  assignBookToShelf(data: AssignBookToShelf): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/assign-book`, data).pipe(
      catchError(this.handleError)
    );
  }

  // Remove book from shelf
  removeBookFromShelf(data: RemoveBookFromShelf): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/remove-book`, data).pipe(
      catchError(this.handleError)
    );
  }

  // Remove book location (new method for unique locations)
  removeBookLocation(bookLocationId: number): Observable<ApiResponse<void>> {
    return this.http.post<ApiResponse<void>>(`${this.apiUrl}/remove-book-location`, { bookLocationId }).pipe(
      catchError(this.handleError)
    );
  }

  // Get available locations in shelf
  getAvailableLocations(shelfId: number): Observable<ShelfLocations> {
    return this.http.get<ShelfLocations>(`${this.apiUrl}/${shelfId}/locations`).pipe(
      catchError(this.handleError)
    );
  }

  // Validate shelf data
  validateShelf(shelf: CreateShelf | UpdateShelf): Observable<ShelfValidation> {
    return this.http.post<ShelfValidation>(`${this.apiUrl}/validate`, shelf).pipe(
      catchError(this.handleError)
    );
  }

  // Get books in storage (not assigned to any shelf)
  getBooksInStorage(): Observable<Book[]> {
    return this.http.get<Book[]>(`${environment.apiUrl}/books/in-storage`).pipe(
      catchError(this.handleError)
    );
  }

  // Get all books on shelves
  getBooksOnShelf(): Observable<BookInShelf[]> {
    return this.http.get<BookInShelf[]>(`${environment.apiUrl}/books/on-shelf`).pipe(
      catchError(this.handleError)
    );
  }

  // Search shelves by name or zone
  searchShelves(query: string): Observable<Shelf[]> {
    return this.http.get<Shelf[]>(`${this.apiUrl}/search?q=${encodeURIComponent(query)}`).pipe(
      catchError(this.handleError)
    );
  }

  // Get shelf statistics
  getShelfStatistics(shelfId: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/${shelfId}/statistics`).pipe(
      catchError(this.handleError)
    );
  }

  // Private error handler
  private handleError = (error: HttpErrorResponse): Observable<never> => {
    let errorMessage = 'Đã xảy ra lỗi không xác định';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Lỗi: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = error.error?.message || 'Dữ liệu không hợp lệ';
          break;
        case 404:
          errorMessage = 'Không tìm thấy kệ sách';
          break;
        case 409:
          errorMessage = 'Kệ sách đã tồn tại hoặc có xung đột dữ liệu';
          break;
        case 500:
          errorMessage = 'Lỗi máy chủ nội bộ';
          break;
        default:
          errorMessage = `Lỗi ${error.status}: ${error.error?.message || error.message}`;
      }
    }

    console.error('ShelfService Error:', error);
    return throwError(() => new Error(errorMessage));
  };
}
