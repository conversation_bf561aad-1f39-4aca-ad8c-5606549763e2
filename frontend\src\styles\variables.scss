// ===================================
// DESIGN SYSTEM VARIABLES
// ===================================

// Color Palette
:root {
  // Primary Colors
  --color-primary: #1976d2;
  --color-primary-variant: #1565c0;
  --color-primary-light: #bbdefb;
  --color-on-primary: #ffffff;

  // Secondary Colors
  --color-secondary: #03dac6;
  --color-secondary-variant: #018786;
  --color-on-secondary: #000000;

  // Surface Colors
  --color-surface: #ffffff;
  --color-surface-variant: #f5f5f5;
  --color-on-surface: #1c1b1f;
  --color-on-surface-variant: #49454f;

  // Background Colors
  --color-background: #fefbff;
  --color-on-background: #1c1b1f;

  // Outline Colors
  --color-outline: #e0e0e0;
  --color-outline-variant: #cac4d0;

  // State Colors
  --color-success: #4caf50;
  --color-success-light: #c8e6c9;
  --color-on-success: #ffffff;

  --color-warning: #ff9800;
  --color-warning-light: #ffe0b2;
  --color-on-warning: #ffffff;

  --color-error: #f44336;
  --color-error-light: #ffcdd2;
  --color-on-error: #ffffff;

  --color-info: #2196f3;
  --color-info-light: #bbdefb;
  --color-on-info: #ffffff;

  // Accent Colors
  --color-accent: #ff4081;
  --color-accent-variant: #f50057;
  --color-on-accent: #ffffff;

  // Neutral Colors
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #eeeeee;
  --color-neutral-300: #e0e0e0;
  --color-neutral-400: #bdbdbd;
  --color-neutral-500: #9e9e9e;
  --color-neutral-600: #757575;
  --color-neutral-700: #616161;
  --color-neutral-800: #424242;
  --color-neutral-900: #212121;
}

// Dark Theme Colors
[data-theme="dark"], .dark-theme {
  --color-primary: #90caf9;
  --color-primary-variant: #64b5f6;
  --color-primary-light: #e3f2fd;
  --color-on-primary: #000000;

  --color-secondary: #80cbc4;
  --color-secondary-variant: #4db6ac;
  --color-on-secondary: #000000;

  --color-surface: #121212;
  --color-surface-variant: #1e1e1e;
  --color-on-surface: #e1e1e1;
  --color-on-surface-variant: #c7c7c7;

  --color-background: #0d1117;
  --color-on-background: #e1e1e1;

  --color-outline: #2d2d2d;
  --color-outline-variant: #404040;

  --color-success: #81c784;
  --color-warning: #ffb74d;
  --color-error: #e57373;
  --color-info: #64b5f6;
}

// Typography Scale
:root {
  // Font Families
  --font-family-primary: 'Inter', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-family-secondary: 'Roboto Slab', Georgia, serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

  // Font Weights
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  // Font Sizes
  --font-size-xs: 0.75rem;    // 12px
  --font-size-sm: 0.875rem;   // 14px
  --font-size-base: 1rem;     // 16px
  --font-size-lg: 1.125rem;   // 18px
  --font-size-xl: 1.25rem;    // 20px
  --font-size-2xl: 1.5rem;    // 24px
  --font-size-3xl: 1.875rem;  // 30px
  --font-size-4xl: 2.25rem;   // 36px
  --font-size-5xl: 3rem;      // 48px

  // Line Heights
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}

// Spacing Scale
:root {
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0_5: 0.125rem;  // 2px
  --spacing-1: 0.25rem;     // 4px
  --spacing-1_5: 0.375rem;  // 6px
  --spacing-2: 0.5rem;      // 8px
  --spacing-2_5: 0.625rem;  // 10px
  --spacing-3: 0.75rem;     // 12px
  --spacing-3_5: 0.875rem;  // 14px
  --spacing-4: 1rem;        // 16px
  --spacing-5: 1.25rem;     // 20px
  --spacing-6: 1.5rem;      // 24px
  --spacing-7: 1.75rem;     // 28px
  --spacing-8: 2rem;        // 32px
  --spacing-9: 2.25rem;     // 36px
  --spacing-10: 2.5rem;     // 40px
  --spacing-11: 2.75rem;    // 44px
  --spacing-12: 3rem;       // 48px
  --spacing-14: 3.5rem;     // 56px
  --spacing-16: 4rem;       // 64px
  --spacing-20: 5rem;       // 80px
  --spacing-24: 6rem;       // 96px
  --spacing-32: 8rem;       // 128px
  --spacing-40: 10rem;      // 160px
  --spacing-48: 12rem;      // 192px
  --spacing-56: 14rem;      // 224px
  --spacing-64: 16rem;      // 256px
}

// Border Radius
:root {
  --radius-none: 0;
  --radius-sm: 0.125rem;     // 2px
  --radius-base: 0.25rem;    // 4px
  --radius-md: 0.375rem;     // 6px
  --radius-lg: 0.5rem;       // 8px
  --radius-xl: 0.75rem;      // 12px
  --radius-2xl: 1rem;        // 16px
  --radius-3xl: 1.5rem;      // 24px
  --radius-full: 9999px;
}

// Shadows
:root {
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

// Z-Index Scale
:root {
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}

// Breakpoints
:root {
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

// Animation & Transitions
:root {
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --transition-slower: 500ms ease-in-out;

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

// Component Specific Variables
:root {
  // Header
  --header-height: 64px;
  --header-height-mobile: 56px;

  // Sidebar
  --sidebar-width: 280px;
  --sidebar-width-collapsed: 64px;

  // Content
  --content-max-width: 1400px;
  --content-padding: var(--spacing-6);
  --content-padding-mobile: var(--spacing-4);

  // Cards
  --card-padding: var(--spacing-6);
  --card-padding-mobile: var(--spacing-4);
  --card-border-radius: var(--radius-xl);

  // Forms
  --form-field-height: 56px;
  --form-field-height-dense: 40px;
  --form-field-border-radius: var(--radius-lg);

  // Buttons
  --button-height: 40px;
  --button-height-large: 48px;
  --button-height-small: 32px;
  --button-border-radius: var(--radius-lg);

  // Tables
  --table-row-height: 56px;
  --table-row-height-dense: 40px;
  --table-header-height: 64px;
}

// Utility Classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Focus Styles
.focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

// Scrollbar Styles
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-surface-variant);
    border-radius: var(--radius-base);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-outline);
    border-radius: var(--radius-base);
    
    &:hover {
      background: var(--color-on-surface);
      opacity: 0.7;
    }
  }
}

// Print Styles
@media print {
  :root {
    --color-primary: #000000;
    --color-on-surface: #000000;
    --color-surface: #ffffff;
    --color-background: #ffffff;
  }

  .no-print {
    display: none !important;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition-base: 0ms;
    --transition-slow: 0ms;
    --transition-slower: 0ms;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// High Contrast Mode
@media (prefers-contrast: high) {
  :root {
    --color-outline: #000000;
    --shadow-sm: 0 0 0 1px #000000;
    --shadow-md: 0 0 0 2px #000000;
    --shadow-lg: 0 0 0 3px #000000;
  }
}
