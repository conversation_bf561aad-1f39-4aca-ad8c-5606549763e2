using LibraryManagement.Core.Entities;
using LibraryManagement.Core.Interfaces;
using LibraryManagement.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace LibraryManagement.Infrastructure.Repositories;

public class BookLocationRepository : Repository<BookLocation>, IBookLocationRepository
{
    public BookLocationRepository(LibraryDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<BookLocation>> GetByBookshelfIdAsync(int bookshelfId)
    {
        return await _context.BookLocations
            .Include(bl => bl.Book)
            .ThenInclude(b => b.Category)
            .Where(bl => bl.BookshelfId == bookshelfId)
            .ToListAsync();
    }

    public async Task<IEnumerable<BookLocation>> GetByBookIdAsync(int bookId)
    {
        return await _context.BookLocations
            .Include(bl => bl.Bookshelf)
            .ThenInclude(bs => bs.Zone)
            .Where(bl => bl.BookId == bookId)
            .ToListAsync();
    }

    public async Task<BookLocation?> GetByLocationAsync(int bookshelfId, string locationCode)
    {
        return await _context.BookLocations
            .Include(bl => bl.Book)
            .FirstOrDefaultAsync(bl => bl.BookshelfId == bookshelfId && bl.LocationCode == locationCode);
    }

    public async Task<bool> IsLocationOccupiedAsync(int bookshelfId, string locationCode)
    {
        return await _context.BookLocations
            .AnyAsync(bl => bl.BookshelfId == bookshelfId && bl.LocationCode == locationCode);
    }
}
