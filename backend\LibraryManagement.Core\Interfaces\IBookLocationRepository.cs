using LibraryManagement.Core.Entities;

namespace LibraryManagement.Core.Interfaces;

public interface IBookLocationRepository : IRepository<BookLocation>
{
    Task<IEnumerable<BookLocation>> GetByBookshelfIdAsync(int bookshelfId);
    Task<IEnumerable<BookLocation>> GetByBookIdAsync(int bookId);
    Task<BookLocation?> GetByLocationAsync(int bookshelfId, string locationCode);
    Task<bool> IsLocationOccupiedAsync(int bookshelfId, string locationCode);
}
