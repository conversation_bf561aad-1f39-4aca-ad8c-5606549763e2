using LibraryManagement.Core.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using LibraryManagement.Application.DTOs;
using LibraryManagement.Core.Entities;

namespace LibraryManagement.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [AllowAnonymous] // Allow anonymous access for all endpoints
    public class BookshelfController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public BookshelfController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            var shelves = await _unitOfWork.Bookshelves
                .Query()
                .Include(s => s.Zone)
                .Include(s => s.Books)
                .OrderByDescending(s => s.CreatedAt) // Sắp xếp kệ mới nhất lên đầu
                .Select(s => new BookshelfDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    ZoneId = s.ZoneId,
                    ZoneName = s.Zone.Name,
                    Capacity = s.Capacity,
                    Status = s.Status,
                    Description = s.Description,
                    CurrentCount = s.Books.Sum(b => b.OnShelfQuantity)
                })
                .ToListAsync();

            return Ok(shelves);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            var shelf = await _unitOfWork.Bookshelves
                .Query()
                .Include(s => s.Zone)
                .Include(s => s.Books)
                .Where(s => s.Id == id)
                .Select(s => new BookshelfDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    ZoneId = s.ZoneId,
                    ZoneName = s.Zone.Name,
                    Capacity = s.Capacity,
                    Status = s.Status,
                    Description = s.Description,
                    CurrentCount = s.Books.Sum(b => b.OnShelfQuantity)
                })
                .FirstOrDefaultAsync();

            if (shelf == null) return NotFound();

            return Ok(shelf);
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CreateShelfDto dto)
        {
            var shelf = new Bookshelf
            {
                Name = dto.Name,
                ZoneId = dto.ZoneId,
                Description = dto.Description,
                Capacity = dto.Capacity,
                CurrentCount = 0,
                Status = dto.Status ?? "Active",
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Bookshelves.AddAsync(shelf);
            await _unitOfWork.SaveAsync();

            return Ok(new { message = "Tạo kệ thành công", shelfId = shelf.Id });
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] CreateShelfDto dto)
        {
            var shelf = await _unitOfWork.Bookshelves.GetByIdAsync(id);
            if (shelf == null) return NotFound();

            shelf.Name = dto.Name;
            shelf.ZoneId = dto.ZoneId;
            shelf.Description = dto.Description;
            shelf.Capacity = dto.Capacity;
            shelf.Status = dto.Status ?? shelf.Status;
            shelf.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Bookshelves.Update(shelf);
            await _unitOfWork.SaveAsync();

            return Ok(shelf);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var shelf = await _unitOfWork.Bookshelves
                .Query()
                .Include(s => s.Books)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (shelf == null) return NotFound();

            // Check if shelf has books
            if (shelf.Books.Any())
            {
                return BadRequest("Không thể xóa kệ đang có sách. Vui lòng di chuyển sách trước khi xóa.");
            }

            _unitOfWork.Bookshelves.Remove(shelf);
            await _unitOfWork.SaveAsync();

            return NoContent();
        }

        [HttpGet("{shelfId}/books")]
        public async Task<IActionResult> GetBooksInShelf(int shelfId)
        {
            var books = await _unitOfWork.Books
                .Query()
                .Include(b => b.Category)
                .Include(b => b.Bookshelf)
                .ThenInclude(s => s.Zone)
                .Where(b => b.BookshelfId == shelfId)
                .Select(b => new
                {
                    BookId = b.Id,
                    Title = b.Title,
                    Author = b.Author,
                    ISBN = b.ISBN,
                    CategoryName = b.Category.Name,
                    Location = $"{b.Bookshelf.Zone.Name} - {b.Bookshelf.Name}" + (b.LocationCode != null ? $" - {b.LocationCode}" : ""),
                    LocationCode = b.LocationCode,
                    StockQuantity = b.OnShelfQuantity
                })
                .ToListAsync();

            return Ok(books);
        }

        [HttpPost("assign-book")]
        public async Task<IActionResult> AssignBookToShelf([FromBody] AssignBookToShelfDto dto)
        {
            var book = await _unitOfWork.Books.GetByIdAsync(dto.BookId);
            if (book == null)
                return NotFound("Sách không tồn tại.");

            var shelf = await _unitOfWork.Bookshelves
                .Query()
                .Include(s => s.Books)
                .FirstOrDefaultAsync(s => s.Id == dto.ShelfId);

            if (shelf == null)
                return NotFound("Kệ không tồn tại.");

            // Check capacity
            if (shelf.Books.Count >= shelf.Capacity)
                return BadRequest("Kệ đã đầy.");

            // Check if location code is unique within shelf
            if (!string.IsNullOrEmpty(dto.LocationCode))
            {
                var existingBook = await _unitOfWork.Books
                    .Query()
                    .FirstOrDefaultAsync(b => b.BookshelfId == dto.ShelfId && b.LocationCode == dto.LocationCode);

                if (existingBook != null)
                    return BadRequest($"Vị trí {dto.LocationCode} đã có sách khác.");
            }

            // Assign book to shelf
            book.BookshelfId = dto.ShelfId;
            book.LocationCode = dto.LocationCode;
            book.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Books.Update(book);
            await _unitOfWork.SaveAsync();

            return Ok(new { message = "Đã gán sách vào kệ thành công." });
        }

        [HttpPost("remove-book")]
        public async Task<IActionResult> RemoveBookFromShelf([FromBody] RemoveBookFromShelfDto dto)
        {
            var book = await _unitOfWork.Books.GetByIdAsync(dto.BookId);
            if (book == null)
                return NotFound("Sách không tồn tại.");

            if (book.BookshelfId == null)
                return BadRequest("Sách không ở trên kệ nào.");

            // Remove book from shelf
            book.BookshelfId = null;
            book.LocationCode = null;
            book.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Books.Update(book);
            await _unitOfWork.SaveAsync();

            return Ok(new { message = "Đã gỡ sách khỏi kệ thành công." });
        }

        [HttpGet("available-locations/{shelfId}")]
        public async Task<IActionResult> GetAvailableLocations(int shelfId)
        {
            var shelf = await _unitOfWork.Bookshelves.GetByIdAsync(shelfId);
            if (shelf == null)
                return NotFound("Kệ không tồn tại.");

            var usedLocations = await _unitOfWork.Books
                .Query()
                .Where(b => b.BookshelfId == shelfId && b.LocationCode != null)
                .Select(b => b.LocationCode)
                .ToListAsync();

            // Generate suggested locations (A1-A5, B1-B5, C1-C5, D1-D5)
            var allLocations = new List<string>();
            var rows = new[] { "A", "B", "C", "D" };
            for (int i = 1; i <= 5; i++)
            {
                foreach (var row in rows)
                {
                    allLocations.Add($"{row}{i}");
                }
            }

            var availableLocations = allLocations.Except(usedLocations).ToList();

            return Ok(new { availableLocations, usedLocations });
        }
    }
}
