import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatNativeDateModule } from '@angular/material/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

import { ReportService, DashboardStats, PopularBook, ActiveMember, CategoryStats, MonthlyStats } from '../../services/report.service';
import { AuthService } from '../../services/auth.service';
import { ThemeService } from '../../services/theme.service';

// Import Chart.js
import { ChartConfiguration, ChartOptions } from 'chart.js';
import { ChartsModule } from '../../shared/charts/charts.module';
import { PageContainerComponent } from "../../shared/page-container/page-container.component";
import { PageHeaderComponent, PageHeaderAction } from "../../shared/page-header/page-header.component";
import { DataVisualizationComponent, ChartData } from "../../shared/data-visualization/data-visualization.component";
import { QuickActionsComponent, QuickAction } from "../../shared/quick-actions/quick-actions.component";
import { NotificationCenterComponent } from "../../shared/notification-center/notification-center.component";

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatProgressBarModule,
    MatTabsModule,
    MatDatepickerModule,
    MatFormFieldModule,
    MatInputModule,
    MatNativeDateModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule,
    ChartsModule,
    PageContainerComponent,
    PageHeaderComponent,
    DataVisualizationComponent,
    QuickActionsComponent,
    NotificationCenterComponent,
  ],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  stats: DashboardStats | null = null;
  popularBooks: PopularBook[] = [];
  activeMembers: ActiveMember[] = [];
  categoryStats: CategoryStats[] = [];
  monthlyStats: MonthlyStats[] = [];
  isLoading = true;
  error = '';

  // Chart data for new components
  monthlyChartData: ChartData = { labels: [], datasets: [] };
  categoryChartData: ChartData = { labels: [], datasets: [] };
  activityChartData: ChartData = { labels: [], datasets: [] };

  // Header and Quick Actions
  headerActions: PageHeaderAction[] = [];
  quickActions: QuickAction[] = [];

  // Chart configurations
  public barChartOptions: ChartOptions = {
    responsive: true,
    scales: {
      x: {},
      y: {
        min: 0
      }
    },
    plugins: {
      legend: {
        display: true,
      },
    }
  };

  public monthlyBarChartData: ChartConfiguration<'bar'>['data'] = {
    labels: [],
    datasets: [
      { data: [], label: 'Mượn mới' },
      { data: [], label: 'Trả sách' },
      { data: [], label: 'Thành viên mới' }
    ]
  };

  public categoryPieChartData: ChartConfiguration<'pie'>['data'] = {
    labels: [],
    datasets: [{
      data: []
    }]
  };

  public pieChartOptions: ChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    }
  };

  constructor(
    private reportService: ReportService,
    private authService: AuthService,
    private themeService: ThemeService,
    private snackBar: MatSnackBar
  ) {
    this.initializeHeaderActions();
    this.initializeQuickActions();
  }

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private initializeHeaderActions(): void {
    this.headerActions = [
      {
        label: 'Làm mới',
        icon: 'refresh',
        color: 'primary',
        click: () => this.loadDashboardData()
      }
    ];
  }

  private initializeQuickActions(): void {
    this.quickActions = [
      {
        label: 'Thêm sách mới',
        icon: 'library_add',
        color: 'primary',
        click: () => window.location.href = '/books/add'
      },
      {
        label: 'Thêm thành viên',
        icon: 'person_add',
        color: 'accent',
        click: () => window.location.href = '/members/add'
      },
      {
        label: 'Mượn sách',
        icon: 'book',
        color: 'warn',
        click: () => window.location.href = '/borrows'
      },
      {
        label: 'Báo cáo',
        icon: 'assessment',
        color: 'primary',
        click: () => window.location.href = '/reports'
      }
    ];
  }

  loadDashboardData(): void {
    this.isLoading = true;
    this.error = '';

    // Load dashboard stats
    this.reportService.getDashboardStats().subscribe({
      next: (data) => {
        this.stats = data;
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Không thể tải dữ liệu tổng quan';
        this.isLoading = false;
        this.snackBar.open(this.error, 'Đóng', { duration: 3000 });
      }
    });

    // Load popular books
    this.reportService.getPopularBooks().subscribe({
      next: (data) => {
        this.popularBooks = data;
      },
      error: (error) => {
        this.snackBar.open('Không thể tải dữ liệu sách phổ biến', 'Đóng', { duration: 3000 });
      }
    });

    // Load active members
    this.reportService.getActiveMembers().subscribe({
      next: (data) => {
        this.activeMembers = data;
      },
      error: (error) => {
        this.snackBar.open('Không thể tải dữ liệu thành viên tích cực', 'Đóng', { duration: 3000 });
      }
    });

    // Load category stats
    this.reportService.getCategoryStats().subscribe({
      next: (data) => {
        this.categoryStats = data;
        this.updateCategoryChart();
      },
      error: (error) => {
        this.snackBar.open('Không thể tải dữ liệu thống kê danh mục', 'Đóng', { duration: 3000 });
      }
    });

    // Load monthly stats
    this.reportService.getMonthlyStats().subscribe({
      next: (data) => {
        this.monthlyStats = data;
        this.updateMonthlyChart();
      },
      error: (error) => {
        this.snackBar.open('Không thể tải dữ liệu thống kê hàng tháng', 'Đóng', { duration: 3000 });
      }
    });
  }

  updateMonthlyChart(): void {
    const labels = this.monthlyStats.map(stat => stat.monthName);
    const borrowData = this.monthlyStats.map(stat => stat.newBorrows);
    const returnData = this.monthlyStats.map(stat => stat.returns);
    const memberData = this.monthlyStats.map(stat => stat.newMembers);

    this.monthlyBarChartData = {
      labels: labels,
      datasets: [
        { data: borrowData, label: 'Mượn mới' },
        { data: returnData, label: 'Trả sách' },
        { data: memberData, label: 'Thành viên mới' }
      ]
    };
  }

  updateCategoryChart(): void {
    const labels = this.categoryStats.map(stat => stat.categoryName);
    const data = this.categoryStats.map(stat => stat.borrowCount);

    this.categoryPieChartData = {
      labels: labels,
      datasets: [{
        data: data
      }]
    };
  }

  getMemberStatusClass(status: number): string {
    switch (status) {
      case 1: return 'status-active';
      case 2: return 'status-suspended';
      case 3: return 'status-expired';
      case 4: return 'status-banned';
      default: return '';
    }
  }

  getMemberStatusText(status: number): string {
    switch (status) {
      case 1: return 'Hoạt động';
      case 2: return 'Tạm ngưng';
      case 3: return 'Hết hạn';
      case 4: return 'Cấm';
      default: return 'Không xác định';
    }
  }

  canViewReports(): boolean {
    return this.authService.canHandleBorrowOperations();
  }
}